"use client";
import { MarkdownRenderer } from "./components/chat/ui/markdown-renderer";

const testContent = `
# Mermaid 图表测试

这是一个测试页面，用于验证Mermaid图表的全屏缩放功能。

## 流程图示例

\`\`\`mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页面]
    B -->|否| D[显示登录页面]
    C --> E[用户操作]
    D --> F[用户登录]
    F --> G{登录成功?}
    G -->|是| C
    G -->|否| H[显示错误信息]
    H --> D
    E --> I[处理请求]
    I --> J[返回结果]
    J --> K[结束]
\`\`\`

## 时序图示例

\`\`\`mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 发起请求
    F->>B: 转发请求
    B->>D: 查询数据
    D-->>B: 返回数据
    B-->>F: 处理结果
    F-->>U: 显示结果
\`\`\`

点击任意图表可以全屏查看，支持：
- 滚轮缩放
- 鼠标拖拽移动
- 工具栏按钮控制
- ESC键或点击外部区域关闭
`;

export default function TestMermaid() {
  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <MarkdownRenderer content={testContent} />
    </div>
  );
}
