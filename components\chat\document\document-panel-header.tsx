"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Download,
  Copy,
  X,
  Edit3
} from "lucide-react";
import { cn } from "@/lib/utils";

interface DocumentPanelHeaderProps {
  t: any;
  isMarkdownPreview: boolean;
  onTogglePreview: (preview: boolean) => void;
  onClose: () => void;
  hasContent: boolean;
  isLoading?: boolean;
  isMobile?: boolean;
  content: string; // 添加内容属性，用于内部处理复制和下载
}

export function DocumentPanelHeader({
  t,
  isMarkdownPreview,
  onTogglePreview,
  onClose,
  hasContent,
  isLoading = false,
  isMobile = false,
  content
}: DocumentPanelHeaderProps) {
  const [copySuccess, setCopySuccess] = useState(false);

  // 下载文档功能
  const handleDownload = () => {
    if (!content.trim()) return;

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'design.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 复制文档内容
  const handleCopy = async () => {
    if (!content.trim()) return;

    try {
      await navigator.clipboard.writeText(content);
      // 显示成功反馈
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000); // 2秒后隐藏
      console.log('Document content copied to clipboard');
    } catch (error) {
      console.error('Copy failed:', error);
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        // 显示成功反馈
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000); // 2秒后隐藏
        console.log('Document content copied to clipboard (fallback)');
      } catch (fallbackError) {
        console.error('Copy failed (fallback):', fallbackError);
      }
      document.body.removeChild(textArea);
    }
  };
  return (
    <div className="flex items-center justify-between p-4 border-b border-border/30 flex-shrink-0">
      <h2 className="font-semibold">{t('messages.design_document')}</h2>
      <div className="flex items-center gap-2">
        {/* 编辑/预览切换按钮 */}
        <div className="flex items-center border rounded-md">
          <Button
            variant={!isMarkdownPreview ? "default" : "ghost"}
            size="sm"
            onClick={() => onTogglePreview(false)}
            className={cn(
              "rounded-r-none border-0 text-xs",
              isMobile ? "px-2" : "px-3"
            )}
          >
            <Edit3 className="w-3 h-3 mr-1" />
            {!isMobile && t('ui.edit')}
          </Button>
          <Button
            variant={isMarkdownPreview ? "default" : "ghost"}
            size="sm"
            onClick={() => onTogglePreview(true)}
            className={cn(
              "rounded-l-none border-0 text-xs",
              isMobile ? "px-2" : "px-3"
            )}
            disabled={!hasContent}
          >
            <FileText className="w-3 h-3 mr-1" />
            {!isMobile && t('ui.preview')}
          </Button>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDownload}
          disabled={!hasContent || isLoading}
          title={t('tooltips.download_file')}
        >
          <Download className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          disabled={!hasContent}
          title={copySuccess ? t('ui.copy_success') : t('ui.copy_document')}
          className={copySuccess ? "text-green-600" : ""}
        >
          {copySuccess ? (
            <span className="flex items-center gap-1">
              <span className="text-green-600">✓</span>
            </span>
          ) : (
            <Copy className="w-4 h-4" />
          )}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
