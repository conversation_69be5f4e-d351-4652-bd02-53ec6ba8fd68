import {
  mysqlTable,
  varchar,
  text,
  boolean,
  int,
  timestamp,
  unique,
  uniqueIndex,
} from "drizzle-orm/mysql-core";
import { sql } from "drizzle-orm";

// Users table
export const users = mysqlTable(
  "users",
  {
    id: int().primaryKey().autoincrement(),
    uuid: varchar({ length: 255 }).notNull().unique(),
    email: varchar({ length: 255 }).notNull(),
    created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
    nickname: varchar({ length: 255 }),
    avatar_url: varchar({ length: 255 }),
    locale: varchar({ length: 50 }),
    signin_type: varchar({ length: 50 }),
    signin_ip: varchar({ length: 255 }),
    signin_provider: varchar({ length: 50 }),
    signin_openid: varchar({ length: 255 }),
    invite_code: varchar({ length: 255 }).notNull().default(""),
    updated_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
    invited_by: varchar({ length: 255 }).notNull().default(""),
    is_affiliate: boolean().notNull().default(false),
    api_key: varchar({ length: 255 }).notNull().default(""),
  },
  (table) => [
    uniqueIndex("email_provider_unique_idx").on(
      table.email,
      table.signin_provider
    ),
  ]
);

// Orders table
export const orders = mysqlTable("orders", {
  id: int().primaryKey().autoincrement(),
  order_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  user_uuid: varchar({ length: 255 }).notNull().default(""),
  user_email: varchar({ length: 255 }).notNull().default(""),
  amount: int().notNull(),
  interval: varchar({ length: 50 }),
  expired_at: timestamp("expired_at").default(sql`CURRENT_TIMESTAMP`),
  status: varchar({ length: 50 }).notNull(),
  stripe_session_id: varchar({ length: 255 }),
  credits: int().notNull(),
  currency: varchar({ length: 50 }),
  sub_id: varchar({ length: 255 }),
  sub_interval_count: int(),
  sub_cycle_anchor: int(),
  sub_period_end: int(),
  sub_period_start: int(),
  sub_times: int(),
  product_id: varchar({ length: 255 }),
  product_name: varchar({ length: 255 }),
  valid_months: int(),
  order_detail: text(),
  paid_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  paid_email: varchar({ length: 255 }),
  paid_detail: text(),
});

// API Keys table
export const apikeys = mysqlTable("apikeys", {
  id: int().primaryKey().autoincrement(),
  api_key: varchar({ length: 255 }).notNull().unique(),
  title: varchar({ length: 100 }),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  status: varchar({ length: 50 }),
});

// Credits table
export const credits = mysqlTable("credits", {
  id: int().primaryKey().autoincrement(),
  trans_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  user_uuid: varchar({ length: 255 }).notNull(),
  trans_type: varchar({ length: 50 }).notNull(),
  credits: int().notNull(),
  order_no: varchar({ length: 255 }),
  expired_at: timestamp("expired_at").default(sql`CURRENT_TIMESTAMP`),
});

// Posts table
export const posts = mysqlTable("posts", {
  id: int().primaryKey().autoincrement(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  slug: varchar({ length: 255 }),
  title: varchar({ length: 255 }),
  description: text(),
  content: text(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  updated_at: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`),
  status: varchar({ length: 50 }),
  cover_url: varchar({ length: 255 }),
  author_name: varchar({ length: 255 }),
  author_avatar_url: varchar({ length: 255 }),
  locale: varchar({ length: 50 }),
});

// Affiliates table
export const affiliates = mysqlTable("affiliates", {
  id: int().primaryKey().autoincrement(),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  status: varchar({ length: 50 }).notNull().default(""),
  invited_by: varchar({ length: 255 }).notNull(),
  paid_order_no: varchar({ length: 255 }).notNull().default(""),
  paid_amount: int().notNull().default(0),
  reward_percent: int().notNull().default(0),
  reward_amount: int().notNull().default(0),
});

// Feedbacks table
export const feedbacks = mysqlTable("feedbacks", {
  id: int().primaryKey().autoincrement(),
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  status: varchar({ length: 50 }),
  user_uuid: varchar({ length: 255 }),
  content: text(),
  rating: int(),
});

// Chat Sessions table
export const chatSessions = mysqlTable("chat_sessions", {
  id: int().primaryKey().autoincrement(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  title: varchar({ length: 255 }).notNull(),
  status: varchar({ length: 50 }).notNull().default("active"), // active, archived, deleted
  chat_state: varchar({ length: 50 }).default("welcome"), // welcome, short_loading, short_confirm, long_loading, long_result
  is_public: boolean().notNull().default(false), // 是否公开分享
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  updated_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
});

// Chat Messages table
export const chatMessages = mysqlTable("chat_messages", {
  id: int().primaryKey().autoincrement(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  session_uuid: varchar({ length: 255 }).notNull(),
  user_uuid: varchar({ length: 255 }).notNull(),
  role: varchar({ length: 50 }).notNull(), // user, assistant
  content: text().notNull(),
  message_type: varchar({ length: 50 }).default("message"), // message, plan, document
  metadata: text(), // JSON string for additional data
  created_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
  updated_at: timestamp().default(sql`CURRENT_TIMESTAMP`),
});
