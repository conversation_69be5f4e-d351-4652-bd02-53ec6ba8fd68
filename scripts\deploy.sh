#!/bin/bash

# GTPlanner <PERSON>bernetes Deployment Script
# This script deploys the application to Kubernetes cluster via Kuboard API

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check required environment variables
check_env_vars() {
    local required_vars=("K8S_TOKEN" "K8S_API_URL" "DEPLOYMENT_NAME" "NAMESPACE" "CI_REGISTRY_IMAGE" "VERSION")
    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi
}

# Function to update deployment
update_deployment() {
    local image_tag="$1"
    
    print_status "Updating deployment with image: $image_tag"
    
    local patch_data=$(cat <<EOF
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "gtagent",
            "image": "$image_tag"
          }
        ],
        "initContainers": [
          {
            "name": "db-migration",
            "image": "$image_tag"
          }
        ]
      }
    }
  }
}
EOF
)

    local response=$(curl -s -w "%{http_code}" -X PATCH \
        -H "content-type: application/strategic-merge-patch+json" \
        -H "Authorization: Bearer $K8S_TOKEN" \
        -d "$patch_data" \
        "$K8S_API_URL/apis/apps/v1/namespaces/$NAMESPACE/deployments/$DEPLOYMENT_NAME")
    
    local http_code="${response: -3}"
    local response_body="${response%???}"
    
    if [[ "$http_code" -eq 200 ]]; then
        print_status "Deployment updated successfully"
        return 0
    else
        print_error "Failed to update deployment. HTTP code: $http_code"
        echo "Response: $response_body"
        return 1
    fi
}

# Function to check deployment status
check_deployment_status() {
    print_status "Checking deployment status..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        local response=$(curl -s -H "Authorization: Bearer $K8S_TOKEN" \
            "$K8S_API_URL/apis/apps/v1/namespaces/$NAMESPACE/deployments/$DEPLOYMENT_NAME")
        
        if [[ $? -ne 0 ]]; then
            print_warning "Failed to get deployment status (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
            continue
        fi
        
        # Check if deployment is progressing
        local progressing_status=$(echo "$response" | jq -r '.status.conditions[] | select(.type=="Progressing") | .status // "Unknown"')
        local available_status=$(echo "$response" | jq -r '.status.conditions[] | select(.type=="Available") | .status // "Unknown"')
        
        print_status "Deployment status - Progressing: $progressing_status, Available: $available_status (attempt $attempt/$max_attempts)"
        
        if [[ "$progressing_status" == "True" && "$available_status" == "True" ]]; then
            print_status "Deployment completed successfully!"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    print_error "Deployment verification timed out after $max_attempts attempts"
    return 1
}

# Main execution
main() {
    print_status "Starting GTPlanner deployment..."
    
    # Check environment variables
    check_env_vars
    
    # Construct image tag with proper sanitization
    local short_sha="${VERSION:0:7}"

    # Get branch name from environment or default to main
    local branch_name="${GITHUB_REF_NAME:-main}"

    # Sanitize branch name for Docker tag compliance
    branch_name="${branch_name//\//-}"    # Replace / with -
    branch_name="${branch_name//_/-}"     # Replace _ with -
    branch_name="${branch_name,,}"        # Convert to lowercase
    branch_name="${branch_name#-}"        # Remove leading -
    branch_name="${branch_name%-}"        # Remove trailing -

    local image_tag="${CI_REGISTRY_IMAGE}:${branch_name}-${short_sha}"
    
    print_status "Deploying to namespace: $NAMESPACE"
    print_status "Deployment name: $DEPLOYMENT_NAME"
    print_status "Image tag: $image_tag"
    
    # Update deployment
    if update_deployment "$image_tag"; then
        # Check deployment status
        if check_deployment_status; then
            print_status "Deployment completed successfully!"
            exit 0
        else
            print_error "Deployment verification failed"
            exit 1
        fi
    else
        print_error "Failed to update deployment"
        exit 1
    fi
}

# Run main function
main "$@"
