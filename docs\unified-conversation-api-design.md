# 统一对话接口设计方案

## 概述

将现有的分散接口（chat、short-planing、long-planing）统一为一个智能对话接口，支持上下文对话和规划卡片输出。

## 接口设计

### 请求接口
```
POST /api/conversation
```

### 请求参数
```typescript
interface ConversationRequest {
  message: string;                    // 用户输入
  conversation_history: ChatMessage[]; // 完整对话历史
  session_id?: string;                // 会话ID（可选）
  language?: string;                  // 语言偏好
  context?: {                         // 上下文信息
    current_plan?: string;            // 当前规划内容
    current_document?: string;        // 当前文档内容
    user_intent_hint?: string;        // 用户意图提示
  };
}

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  message_type: 'message' | 'plan' | 'document';
  timestamp: number;
  metadata?: any;
}
```

### 响应格式
```typescript
interface ConversationResponse {
  success: boolean;
  data: {
    response: string;                 // 文本回复
    intent: 'conversation' | 'requirement' | 'optimization' | 'document_generation';
    confidence: number;               // 意图识别置信度 (0-1)
    actions?: ConversationAction[];   // 可选的操作列表
    metadata?: {
      language: string;               // 检测到的语言
      processing_time: number;        // 处理时间
      model_used: string;            // 使用的模型
    };
  };
  error?: string;
}

interface ConversationAction {
  type: 'plan' | 'document' | 'suggestion';
  content: string;                    // 规划内容、文档内容或建议
  title?: string;                     // 操作标题
  metadata?: {
    version?: number;                 // 版本号
    based_on?: string;               // 基于的内容
    confidence?: number;             // 生成置信度
  };
}
```

## 意图识别逻辑

### 1. conversation（普通对话）
- 问候、感谢、询问概念
- 对已有内容的讨论和澄清
- 一般性咨询

### 2. requirement（项目需求）
- 明确要求创建、开发、设计具体系统
- 描述功能需求和技术方案
- 触发短规划生成

### 3. optimization（优化改进）
- 对现有规划的修改建议
- 基于用户反馈的优化
- 触发规划更新

### 4. document_generation（文档生成）
- 明确要求生成详细文档
- 基于现有规划生成文档
- 触发长文档生成

## 处理流程

```mermaid
flowchart TD
    A[用户输入] --> B[意图识别]
    B --> C{意图类型}
    
    C -->|conversation| D[生成对话回复]
    C -->|requirement| E[生成短规划]
    C -->|optimization| F[优化现有规划]
    C -->|document_generation| G[生成长文档]
    
    D --> H[返回响应]
    E --> I[返回规划+回复]
    F --> J[返回优化规划+回复]
    G --> K[返回文档+回复]
    
    H --> L[前端渲染]
    I --> L
    J --> L
    K --> L
```

## 前端状态简化

### 新的状态枚举
```typescript
enum ChatState {
  WELCOME = "welcome",      // 欢迎页面
  CONVERSATION = "conversation", // 统一对话状态
  LOADING = "loading"       // 处理中状态
}
```

### 渲染逻辑
基于消息类型和actions进行渲染，而不是基于状态：

```typescript
// 根据响应类型渲染不同组件
if (response.actions) {
  response.actions.forEach(action => {
    switch(action.type) {
      case 'plan':
        renderPlanCard(action);
        break;
      case 'document':
        renderDocumentPanel(action);
        break;
      case 'suggestion':
        renderSuggestion(action);
        break;
    }
  });
}
```

## 优势

1. **对话连续性**：用户可以在任何时候进行正常对话
2. **智能响应**：AI在一次调用中决定响应类型
3. **状态简化**：前端状态管理大幅简化
4. **扩展性强**：易于添加新的意图类型和操作
5. **向后兼容**：现有的消息类型和数据结构保持不变

## 实现计划

1. **后端实现**：创建统一的conversation端点
2. **前端重构**：简化状态管理和API调用
3. **渐进迁移**：保留现有接口，逐步迁移
4. **测试验证**：确保功能完整性和用户体验
