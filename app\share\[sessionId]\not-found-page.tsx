"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { FileX } from "lucide-react";
import { useTranslations } from "next-intl";

export function NotFoundPage() {
  const t = useTranslations('Share');
  
  const handleGoToApp = () => {
    window.open(window.location.origin, '_blank');
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
            <FileX className="h-8 w-8 text-muted-foreground" />
          </div>
          <CardTitle className="text-xl">{t('not_found.title')}</CardTitle>
          <CardDescription>
            {t('not_found.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground">
            {t('not_found.description')}
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={handleGoToApp}>
            {t('not_found.go_to_app')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
