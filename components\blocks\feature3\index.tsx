import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature3({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-24 lg:py-32">
      <div className="container">
        <div className="mb-20 max-w-4xl mx-auto text-center">
          {section.label && (
            <Badge variant="outline" className="mb-6">
              {section.label}
            </Badge>
          )}
          <h2 className="mb-8 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mb-8 max-w-3xl mx-auto text-muted-foreground lg:text-lg">
            {section.description}
          </p>
        </div>
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {section.items?.map((item, index) => (
              <div
                key={index}
                className="group relative rounded-lg border bg-background p-6 shadow-sm transition-all hover:shadow-md hover:scale-105"
              >
                <div className="mb-4 flex items-center gap-3">
                  <span className="flex size-8 items-center justify-center rounded-full bg-primary text-primary-foreground font-mono text-sm font-medium">
                    {index + 1}
                  </span>
                </div>
                <h3 className="mb-3 text-lg font-semibold">
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
          {/* Removed image sections - now using clean card layout */}
        </div>
      </div>
    </section>
  );
}
