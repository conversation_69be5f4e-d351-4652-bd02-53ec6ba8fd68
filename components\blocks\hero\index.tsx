import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import Link from "next/link";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  return (
    <>
      <section className="py-32 md:py-40 bg-gradient-to-br from-background via-background to-muted/30">
        <div className="container">
          {/* Removed badge image section */}
          <div className="text-center max-w-5xl mx-auto">
            {hero.announcement && (
              <a
                href={hero.announcement.url}
                className="mx-auto mb-8 inline-flex items-center gap-3 rounded-full border px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-shadow"
              >
                {hero.announcement.label && (
                  <Badge className="text-xs">{hero.announcement.label}</Badge>
                )}
                {hero.announcement.title}
              </a>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-8 mt-6 max-w-4xl text-balance text-5xl font-bold leading-tight lg:mb-10 lg:text-8xl lg:leading-[1.1]">
                {texts[0]}
                <span className="bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent">
                  {highlightText}
                </span>
                {texts[1]}
              </h1>
            ) : (
              <h1 className="mx-auto mb-8 mt-6 max-w-4xl text-balance text-5xl font-bold leading-tight lg:mb-10 lg:text-8xl lg:leading-[1.1]">
                {hero.title}
              </h1>
            )}

            <p
              className="mx-auto max-w-4xl text-lg text-muted-foreground lg:text-xl xl:text-2xl leading-relaxed"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />
            {hero.buttons && (
              <div className="mt-12 flex flex-col justify-center gap-6 sm:flex-row lg:mt-16">
                {hero.buttons.map((item, i) => {
                  return (
                    <Link
                      key={i}
                      href={item.url || ""}
                      target={item.target || ""}
                      className="flex items-center"
                    >
                      <Button
                        className="w-full px-8 py-6 text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105"
                        size="lg"
                        variant={item.variant || "default"}
                      >
                        {item.title}
                        {item.icon && (
                          <Icon name={item.icon} className="ml-2 w-5 h-5" />
                        )}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            )}
            {hero.tip && (
              <p className="mt-12 text-lg text-muted-foreground font-medium">{hero.tip}</p>
            )}
            {hero.show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </>
  );
}
