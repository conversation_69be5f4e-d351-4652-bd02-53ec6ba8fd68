import { Section as SectionType } from "@/types/blocks/section";

export default function Branding({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container flex flex-row items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <h2 className="text-center: text-muted-foreground lg:text-left">
            {section.title}
          </h2>
          <div className="flex flex-wrap items-center justify-center gap-8 mt-4">
            {/* Removed brand logo images */}
            {section.items?.map((item, idx) => (
              <div key={idx} className="text-lg font-semibold text-muted-foreground">
                {item.title}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
