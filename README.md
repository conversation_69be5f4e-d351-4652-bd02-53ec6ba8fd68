# GTPlanner - Planner for Vibe Coding

> AI-powered planning tool for modern developers. Design systems, generate docs, and enhance your vibe coding workflow.

![GTPlanner Preview](preview.png)

## 🎯 What is GTPlanner?

GTPlanner is an intelligent planning tool designed specifically for modern developers who embrace **vibe coding** - the seamless flow of AI-assisted development. It helps you:

- **Plan Complex Systems**: Generate intelligent project plans and system architectures with AI assistance
- **Create Documentation**: Automatically generate comprehensive technical documentation from your planning sessions
- **Enhance Workflow**: Optimize outputs for modern AI development tools like Cursor, Windsurf, and GitHub Copilot
- **Collaborate Effectively**: Export and share plans in multiple formats for team collaboration

## ✨ Key Features

- 🤖 **AI Planning Assistant** - Advanced AI analyzes requirements and generates structured project plans
- 💬 **Interactive Chat Interface** - Natural conversation flow for refining plans iteratively
- 📄 **Document Generation** - Auto-create technical docs, API specs, and architecture diagrams
- 🔧 **Vibe Coding Ready** - Optimized for modern AI development tools
- 🌍 **Multi-language Support** - Full internationalization with seamless language switching
- 📤 **Export & Share** - Multiple export formats for team collaboration

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm

### Installation

1. **Clone the repository**

```bash
git clone https://github.com/gtplanner/gtplanner-frontend.git
cd gtplanner-frontend
```

2. **Install dependencies**

```bash
pnpm install
# or
npm install
```

3. **Set up environment variables**

```bash
cp .env.example .env.local
```

Edit `.env.local` and configure your environment variables:

```env
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Next.js Configuration
NEXT_PUBLIC_WEB_URL=http://localhost:3000
```
### Database Configuration

```env
   DATABASE_URL=mysql://username:password@localhost:5432/gtplanner
```
```bash
pnpm db:migrate
```





4. **Run the development server**

```bash
pnpm dev
# or
npm run dev
```

5. **Open your browser**

Navigate to [http://localhost:3000](http://localhost:3000) to see GTPlanner in action!

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **AI Integration**: OpenAI GPT-4
- **Internationalization**: next-intl
- **State Management**: React Hooks
- **Deployment**: Vercel / Cloudflare Pages

## 📁 Project Structure

```
gtplanner-frontend/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   │   ├── (default)/     # Default layout group
│   │   │   ├── chat/      # Chat interface
│   │   │   └── page.tsx   # Landing page
│   │   └── layout.tsx     # Root layout
│   ├── api/               # API routes
│   │   └── agent-builder/ # AI planning endpoints
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── blocks/           # Landing page blocks
│   ├── ui/               # shadcn/ui components
│   └── ...
├── i18n/                 # Internationalization
│   ├── messages/         # Translation files
│   └── pages/           # Page-specific content
├── lib/                  # Utility functions
├── services/            # API services
└── types/               # TypeScript definitions
```

## 🎨 Customization

### Theme Customization

Customize your theme in `app/globals.css` or use the shadcn/ui theme generator (URL configured in environment variables).

### Landing Page Content

Update landing page content in:
- `i18n/pages/landing/en.json` (English)
- `i18n/pages/landing/zh.json` (Chinese)

### Internationalization

Add new languages or update translations in:
- `i18n/messages/en.json` (English messages)
- `i18n/messages/zh.json` (Chinese messages)

### Chat Interface

The main chat interface is located in `app/[locale]/(default)/chat/page.tsx`. Customize:
- AI planning prompts
- UI components and styling
- State management logic
- Export functionality

## 🚀 Deployment

### Deploy to Vercel (Recommended)

1. **Fork this repository** to your GitHub account

2. **Connect to Vercel**
   - Go to [Vercel](https://vercel.com)
   - Import your forked repository
   - Configure environment variables in Vercel dashboard

3. **Set environment variables** in Vercel:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   NEXT_PUBLIC_WEB_URL=https://your-domain.vercel.app
   ```

4. **Deploy** - Vercel will automatically deploy your application

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fgtplanner%2Fgtplanner-frontend&project-name=gtplanner&repository-name=gtplanner)

### Deploy to Cloudflare Pages

1. **Prepare environment files**

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

2. **Configure environment variables** in `.env.production` and `wrangler.toml`

3. **Deploy**

```bash
npm run cf:deploy
```

## 🔧 Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm type-check` - Run TypeScript checks

### API Endpoints

- `POST /api/agent-builder/short-planing` - Generate short-term planning
- `POST /api/agent-builder/long-planing` - Generate comprehensive documentation

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API key for AI features | Yes |
| `NEXT_PUBLIC_WEB_URL` | Public URL of your application | Yes |

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Code Style

- Use TypeScript for all new code
- Follow the existing code style and conventions
- Run `pnpm lint` before committing
- Write meaningful commit messages

### Reporting Issues

Please use the [GitHub Issues](https://github.com/gtplanner/gtplanner-frontend/issues) to report bugs or request features.

## 📚 Documentation

- **Live Demo**: [Try GTPlanner](https://vibecoding.sop.best)
- **API Documentation**: Available in the `/api` directory
- **Component Documentation**: Check individual component files for JSDoc comments

## 🌟 Use Cases

### For Individual Developers
- Plan personal projects and side hustles
- Generate technical documentation quickly
- Optimize workflow with AI-assisted planning

### For Development Teams
- Collaborate on system architecture design
- Standardize documentation practices
- Share planning templates across projects

### For AI-First Development
- Integrate with Cursor, Windsurf, GitHub Copilot
- Generate prompts optimized for AI coding assistants
- Maintain coding flow with structured planning

## 🙏 Acknowledgments

GTPlanner is built with amazing open-source technologies:

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful UI components
- [OpenAI](https://openai.com/) - AI capabilities
- [Vercel](https://vercel.com/) - Deployment platform

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- **Website**: [GTPlanner Website](https://vibecoding.sop.best) (URL configured via NEXT_PUBLIC_SITE_URL)
- **GitHub**: [GTPlanner GitHub](https://github.com/gtplanner) (URL configured via NEXT_PUBLIC_GITHUB_URL)
- **Issues**: [Report a bug](https://github.com/gtplanner/gtplanner-frontend/issues)

---

**Made with ❤️ for the developer community**

*Transform your development workflow with AI-powered planning. Start coding with confidence.*
