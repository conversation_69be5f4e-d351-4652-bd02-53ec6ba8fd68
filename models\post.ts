import { posts } from "@/db/schema";
import { db } from "@/db";
import { and, desc, eq } from "drizzle-orm";

export enum PostStatus {
  Created = "created",
  Deleted = "deleted",
  Online = "online",
  Offline = "offline",
}

export async function insertPost(
  data: typeof posts.$inferInsert | any
): Promise<typeof posts.$inferSelect | undefined> {
  // Convert string timestamps to Date objects for MySQL compatibility
  const dbData = {
    ...data,
    created_at: data.created_at ? (typeof data.created_at === 'string' ? new Date(data.created_at) : data.created_at) : undefined,
    updated_at: data.updated_at ? (typeof data.updated_at === 'string' ? new Date(data.updated_at) : data.updated_at) : undefined,
  };
  
  await db().insert(posts).values(dbData);

  // For MySQL, query the inserted record using unique field
  if (data.uuid) {
    return await findPostByUuid(data.uuid);
  }

  return undefined;
}

export async function updatePost(
  uuid: string,
  data: Partial<typeof posts.$inferInsert>
): Promise<typeof posts.$inferSelect | undefined> {
  await db()
    .update(posts)
    .set(data)
    .where(eq(posts.uuid, uuid));

  // For MySQL, query the updated record
  return await findPostByUuid(uuid);
}

export async function findPostByUuid(
  uuid: string
): Promise<typeof posts.$inferSelect | undefined> {
  const [post] = await db()
    .select()
    .from(posts)
    .where(eq(posts.uuid, uuid))
    .limit(1);

  return post;
}

export async function findPostBySlug(
  slug: string,
  locale: string
): Promise<typeof posts.$inferSelect | undefined> {
  const [post] = await db()
    .select()
    .from(posts)
    .where(and(eq(posts.slug, slug), eq(posts.locale, locale)))
    .limit(1);

  return post;
}

export async function getAllPosts(
  page: number = 1,
  limit: number = 50
): Promise<(typeof posts.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(posts)
    .orderBy(desc(posts.created_at))
    .limit(limit)
    .offset(offset);

  return data;
}

export async function getPostsByLocale(
  locale: string,
  page: number = 1,
  limit: number = 50
): Promise<(typeof posts.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(posts)
    .where(and(eq(posts.locale, locale), eq(posts.status, PostStatus.Online)))
    .orderBy(desc(posts.created_at))
    .limit(limit)
    .offset(offset);

  return data;
}

export async function getPostsTotal(): Promise<number> {
  const total = await db().$count(posts);

  return total;
}
