"use client";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";

interface Example {
  id: string;
  title: string;
  description: string;
  prompt: string;
}

interface WelcomePageProps {
  examples: Example[];
  onExampleClick: (example: Example) => void;
  t: any;
}

export function WelcomePage({ examples, onExampleClick, t }: WelcomePageProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] space-y-6 px-4 py-8 pb-24">
      <div className="text-center space-y-6 max-w-3xl">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 shadow-lg">
            <Sparkles className="w-8 h-8 text-primary" />
          </div>
        </div>
        <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent leading-tight">
          {t('welcome.title')}
        </h1>
        <p className="text-lg md:text-xl text-muted-foreground/80 leading-relaxed max-w-2xl mx-auto">
          {t('welcome.subtitle')}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 w-full max-w-5xl">
        {examples.map((example, index) => (
          <Card
            key={example.id}
            className="group cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl active:scale-95 bg-gradient-to-br from-background to-muted/20 border-border/40 card-hover-lift"
            onClick={() => onExampleClick(example)}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <CardContent className="p-4 text-center space-y-3 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-3xl"></div>

              <div className="relative z-10">
                <h3 className="text-base font-bold text-foreground group-hover:text-primary transition-colors duration-200 mb-2">
                  {example.title}
                </h3>
                <p className="text-sm text-muted-foreground/80 leading-relaxed mb-3">
                  {example.description}
                </p>
              </div>

              <div className="flex items-center justify-center gap-2 text-primary group-hover:gap-3 transition-all duration-200 pt-1">
                <span className="text-sm font-medium">{t('welcome.click_to_start')}</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center text-sm text-muted-foreground/60 max-w-md mt-8 mb-8">
        <p className="leading-relaxed px-4">{t('welcome.instruction')}</p>
      </div>
    </div>
  );
}
