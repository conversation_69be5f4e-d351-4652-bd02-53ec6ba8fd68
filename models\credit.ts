import { credits } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, gte, asc } from "drizzle-orm";

export async function insertCredit(
  data: typeof credits.$inferSelect | any
): Promise<typeof credits.$inferSelect | undefined> {
  // Convert string timestamps to Date objects for MySQL compatibility
  const dbData = {
    ...data,
    created_at: data.created_at ? (typeof data.created_at === 'string' ? new Date(data.created_at) : data.created_at) : undefined,
    expired_at: data.expired_at ? (typeof data.expired_at === 'string' ? new Date(data.expired_at) : data.expired_at) : undefined,
  };
  
  await db().insert(credits).values(dbData);

  // For MySQL, query the inserted record using unique field
  if (data.trans_no) {
    return await findCreditByTransNo(data.trans_no);
  }

  return undefined;
}

export async function findCreditByTransNo(
  trans_no: string
): Promise<typeof credits.$inferSelect | undefined> {
  const [credit] = await db()
    .select()
    .from(credits)
    .where(eq(credits.trans_no, trans_no))
    .limit(1);

  return credit;
}

export async function findCreditByOrderNo(
  order_no: string
): Promise<typeof credits.$inferSelect | undefined> {
  const [credit] = await db()
    .select()
    .from(credits)
    .where(eq(credits.order_no, order_no))
    .limit(1);

  return credit;
}

export async function getUserValidCredits(
  user_uuid: string
): Promise<(typeof credits.$inferSelect)[] | undefined> {
  const now = new Date().toISOString();
  const data = await db()
    .select()
    .from(credits)
    .where(
      and(
        gte(credits.expired_at, new Date(now)),
        eq(credits.user_uuid, user_uuid)
      )
    )
    .orderBy(asc(credits.expired_at));

  return data;
}

export async function getCreditsByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 50
): Promise<(typeof credits.$inferSelect)[] | undefined> {
  const data = await db()
    .select()
    .from(credits)
    .where(eq(credits.user_uuid, user_uuid))
    .orderBy(desc(credits.created_at))
    .limit(limit)
    .offset((page - 1) * limit);

  return data;
}
