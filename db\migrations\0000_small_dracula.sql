CREATE TABLE `affiliates` (
	`id` int AUTO_INCREMENT NOT NULL,
	`user_uuid` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`status` varchar(50) NOT NULL DEFAULT '',
	`invited_by` varchar(255) NOT NULL,
	`paid_order_no` varchar(255) NOT NULL DEFAULT '',
	`paid_amount` int NOT NULL DEFAULT 0,
	`reward_percent` int NOT NULL DEFAULT 0,
	`reward_amount` int NOT NULL DEFAULT 0,
	CONSTRAINT `affiliates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `apikeys` (
	`id` int AUTO_INCREMENT NOT NULL,
	`api_key` varchar(255) NOT NULL,
	`title` varchar(100),
	`user_uuid` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`status` varchar(50),
	CONSTRAINT `apikeys_id` PRIMARY KEY(`id`),
	CONSTRAINT `apikeys_api_key_unique` UNIQUE(`api_key`)
);
--> statement-breakpoint
CREATE TABLE `credits` (
	`id` int AUTO_INCREMENT NOT NULL,
	`trans_no` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`user_uuid` varchar(255) NOT NULL,
	`trans_type` varchar(50) NOT NULL,
	`credits` int NOT NULL,
	`order_no` varchar(255),
	`expired_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `credits_id` PRIMARY KEY(`id`),
	CONSTRAINT `credits_trans_no_unique` UNIQUE(`trans_no`)
);
--> statement-breakpoint
CREATE TABLE `feedbacks` (
	`id` int AUTO_INCREMENT NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`status` varchar(50),
	`user_uuid` varchar(255),
	`content` text,
	`rating` int,
	CONSTRAINT `feedbacks_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `orders` (
	`id` int AUTO_INCREMENT NOT NULL,
	`order_no` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`user_uuid` varchar(255) NOT NULL DEFAULT '',
	`user_email` varchar(255) NOT NULL DEFAULT '',
	`amount` int NOT NULL,
	`interval` varchar(50),
	`expired_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`status` varchar(50) NOT NULL,
	`stripe_session_id` varchar(255),
	`credits` int NOT NULL,
	`currency` varchar(50),
	`sub_id` varchar(255),
	`sub_interval_count` int,
	`sub_cycle_anchor` int,
	`sub_period_end` int,
	`sub_period_start` int,
	`sub_times` int,
	`product_id` varchar(255),
	`product_name` varchar(255),
	`valid_months` int,
	`order_detail` text,
	`paid_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`paid_email` varchar(255),
	`paid_detail` text,
	CONSTRAINT `orders_id` PRIMARY KEY(`id`),
	CONSTRAINT `orders_order_no_unique` UNIQUE(`order_no`)
);
--> statement-breakpoint
CREATE TABLE `posts` (
	`id` int AUTO_INCREMENT NOT NULL,
	`uuid` varchar(255) NOT NULL,
	`slug` varchar(255),
	`title` varchar(255),
	`description` text,
	`content` text,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`status` varchar(50),
	`cover_url` varchar(255),
	`author_name` varchar(255),
	`author_avatar_url` varchar(255),
	`locale` varchar(50),
	CONSTRAINT `posts_id` PRIMARY KEY(`id`),
	CONSTRAINT `posts_uuid_unique` UNIQUE(`uuid`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` int AUTO_INCREMENT NOT NULL,
	`uuid` varchar(255) NOT NULL,
	`email` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`nickname` varchar(255),
	`avatar_url` varchar(255),
	`locale` varchar(50),
	`signin_type` varchar(50),
	`signin_ip` varchar(255),
	`signin_provider` varchar(50),
	`signin_openid` varchar(255),
	`invite_code` varchar(255) NOT NULL DEFAULT '',
	`updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`invited_by` varchar(255) NOT NULL DEFAULT '',
	`is_affiliate` boolean NOT NULL DEFAULT false,
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_uuid_unique` UNIQUE(`uuid`),
	CONSTRAINT `email_provider_unique_idx` UNIQUE(`email`,`signin_provider`)
);
