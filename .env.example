# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "GTPlanner"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
#DATABASE_URL = postgresql://postgres:<EMAIL>:5432/postgres
#DATABASE_URL = mysql://gtplanner:<EMAIL>:3306/gtplanner

DATABASE_URL = mysql://root:200427@localhost:3306/gtplanner

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_URL = "http://localhost:3000"
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# Casdoor Auth
# https://casdoor.org/docs/integration/javascript/next-auth
CASDOOR_ENDPOINT = "https://sso.sensedeal.cn"
CASDOOR_CLIENT_ID = "b7c7ae2167aa5a76354f"
CASDOOR_CLIENT_SECRET = "80984cb4d0511d9d8fbd758cbcae5bdb4b3f0e4a"
CASDOOR_ORGANIZATION_NAME = "TAB"
CASDOOR_APPLICATION_NAME = "TAB-gtplanner"
NEXT_PUBLIC_AUTH_CASDOOR_ENABLED = "true"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

SHORT_PLANNING_BASEURL = http://nodeport.sensedeal.vip:30897/gt-planner  # start your gtplanner backend

HIGRESS_CONSOLE_URL =  'http://nodeport.sensedeal.vip:31800'
HIGRESS_USERNAME =  'admin'
HIGRESS_PASSWORD = 'adminxch123'

# -----------------------------------------------------------------------------
# External URLs and Links
# -----------------------------------------------------------------------------

# Social Media URLs
NEXT_PUBLIC_GITHUB_URL = "https://github.com/OpenSQZ/GTPlanner"
NEXT_PUBLIC_GITHUB_TEMPLATE_URL = "https://github.com/OpenSQZ/GTPlanner"
NEXT_PUBLIC_DISCORD_URL = "https://discord.gg/K4nSHZKP"
NEXT_PUBLIC_TWITTER_URL = "https://x.com/opensqz"

# Contact Information
NEXT_PUBLIC_SUPPORT_EMAIL = "<EMAIL>"

# Site URLs
NEXT_PUBLIC_SITE_URL = "https://vibecoding.sop.best"
NEXT_PUBLIC_HOMEPAGE_URL = "https://vibecoding.sop.best"

# Documentation URLs (just leave if unmodified)
NEXT_PUBLIC_SHADCN_THEMES_URL = "https://ui.shadcn.com/themes"
NEXT_PUBLIC_GITHUB_CLI_URL = "https://cli.github.com/"
NEXT_PUBLIC_TAB_AI_DOCS_URL = "https://docs.TAB.ai"

# TAB.ai URLs
NEXT_PUBLIC_TAB_AI_URL = "https://TAB.ai"