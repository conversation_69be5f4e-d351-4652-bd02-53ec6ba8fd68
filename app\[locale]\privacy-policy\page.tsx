import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations();

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/privacy-policy`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/privacy-policy`;
  }

  return {
    title: t("legal.privacy_policy.title", { default: "Privacy Policy" }),
    description: t("legal.privacy_policy.description", { 
      default: "Privacy Policy for GTPlanner - Learn how we collect, use, and protect your personal information" 
    }),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PrivacyPolicyPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Back Button */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100">
            <Link href={`/${locale === "en" ? "" : locale}`} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("common.back", { default: "Back" })}
            </Link>
          </Button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t("legal.privacy_policy.title")}
          </h1>
          <div className="w-24 h-1 bg-green-600 mx-auto rounded-full"></div>
        </div>

        {/* Content Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12">
          <div className="prose prose-lg dark:prose-invert max-w-none">

            {/* Introduction */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.privacy_policy.introduction.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {t("legal.privacy_policy.introduction.content")}
              </p>
            </section>

            {/* Information Collection */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.privacy_policy.information_collection.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                {t("legal.privacy_policy.information_collection.intro")}
              </p>

              {/* Data Collection Types */}
              <div className="grid gap-6 md:gap-8">
                {[
                  { key: "account_information", icon: "👤" },
                  { key: "usage_data", icon: "📊" },
                  { key: "device_information", icon: "💻" },
                  { key: "cookies", icon: "🍪" },
                  { key: "payment_information", icon: "💳" }
                ].map(({ key, icon }) => (
                  <div key={key} className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border-l-4 border-blue-500">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <span className="text-2xl mr-3">{icon}</span>
                      {t(`legal.privacy_policy.${key}.title`)}
                    </h3>
                    <div className="space-y-3 text-gray-700 dark:text-gray-300">
                      <div>
                        <strong className="text-gray-900 dark:text-white">
                          {t(`legal.privacy_policy.${key}.what_we_collect`)}:
                        </strong>{" "}
                        {t(`legal.privacy_policy.${key}.what_we_collect_desc`)}
                      </div>
                      <div>
                        <strong className="text-gray-900 dark:text-white">
                          {t(`legal.privacy_policy.${key}.purpose`)}:
                        </strong>{" "}
                        {t(`legal.privacy_policy.${key}.purpose_desc`)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Data Sharing */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.privacy_policy.data_sharing.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.privacy_policy.data_sharing.intro")}
              </p>
              <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.privacy_policy.data_sharing.service_providers")}</strong>: {t("legal.privacy_policy.data_sharing.service_providers_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.privacy_policy.data_sharing.legal_requirements")}</strong>: {t("legal.privacy_policy.data_sharing.legal_requirements_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.privacy_policy.data_sharing.business_transfers")}</strong>: {t("legal.privacy_policy.data_sharing.business_transfers_desc")}
                  </div>
                </li>
              </ul>
            </section>

            {/* User Rights */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.privacy_policy.user_rights.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.privacy_policy.user_rights.intro")}
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                {[
                  { key: "access", icon: "🔍" },
                  { key: "correction", icon: "✏️" },
                  { key: "deletion", icon: "🗑️" },
                  { key: "portability", icon: "📦" },
                  { key: "objection", icon: "🚫" }
                ].map(({ key, icon }) => (
                  <div key={key} className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                      <span className="mr-2">{icon}</span>
                      {t(`legal.privacy_policy.user_rights.${key}`)}
                    </h3>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {t(`legal.privacy_policy.user_rights.${key}_desc`)}
                    </p>
                  </div>
                ))}
              </div>
            </section>

            {/* Additional Sections */}
            {[
              "data_security",
              "data_retention",
              "international_transfers",
              "children_privacy",
              "policy_changes"
            ].map((key) => (
              <section key={key} className="mb-10">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                  {t(`legal.privacy_policy.${key}.title`)}
                </h2>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {t(`legal.privacy_policy.${key}.content`)}
                </p>
              </section>
            ))}

            {/* Contact Information */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.privacy_policy.contact.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.privacy_policy.contact.content")}
              </p>
              <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                <div className="space-y-2">
                  <div>
                    <strong className="text-gray-900 dark:text-white">
                      {t("legal.privacy_policy.contact.copyright_owner")}:
                    </strong>{" "}
                    <a
                      href={`http://${t("common.website_url")}`}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline font-medium"
                    >
                      {t("common.website_url")}
                    </a>
                  </div>
                  <div>
                    <strong className="text-gray-900 dark:text-white">
                      {t("legal.privacy_policy.contact.email")}:
                    </strong>{" "}
                    <a
                      href={`mailto:${t("common.support_email")}`}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline font-medium"
                    >
                      {t("common.support_email")}
                    </a>
                  </div>
                </div>
              </div>
            </section>

            {/* Footer */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mt-12">
              <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center leading-relaxed">
                  {t("legal.privacy_policy.consent")}
                </p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
