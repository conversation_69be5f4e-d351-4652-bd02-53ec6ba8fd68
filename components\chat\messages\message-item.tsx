"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { MarkdownRenderer } from "../ui/markdown-renderer";
import { FileText, Clock } from "lucide-react";

interface MessageItemProps {
  message: {
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: number;
    type?: 'plan' | 'message' | 'document' | 'analysis';
  };
  locale: string;
  t: any;
  onViewDocument?: (content: string) => void;
  isSharedView?: boolean;
}

export function MessageItem({ message, locale, t, onViewDocument, isSharedView = false }: MessageItemProps) {
  return (
    <div className={cn(
      "flex group",
      message.role === 'user' ? "justify-end" : "justify-start"
    )}>
      <div className={cn(
        "max-w-[85%] md:max-w-[80%] rounded-2xl px-4 py-3 shadow-sm transition-all duration-200",
        message.role === 'user'
          ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-primary/20 hover:shadow-primary/30"
          : message.type === 'plan'
          ? "bg-gradient-to-br from-muted/60 to-muted/40 border border-border/40 hover:border-border/60 shadow-sm hover:shadow-md"
          : message.type === 'document'
          ? "bg-gradient-to-br from-blue-50/80 to-blue-100/60 dark:from-blue-950/40 dark:to-blue-900/30 border border-blue-200/60 dark:border-blue-800/60 shadow-blue-100/50 dark:shadow-blue-900/20"
          : "bg-gradient-to-br from-muted/60 to-muted/40 shadow-sm hover:shadow-md"
      )}>
        {message.type === 'plan' ? (
          <div className="space-y-2">
            <MarkdownRenderer content={message.content} />
          </div>
        ) : message.type === 'document' ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm font-semibold text-blue-700 dark:text-blue-300">
                <div className="p-1.5 rounded-lg bg-blue-100 dark:bg-blue-900/50">
                  <FileText className="w-4 h-4" />
                </div>
                <span>{t('messages.document_version')}</span>
              </div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground bg-white/60 dark:bg-black/20 px-2 py-1 rounded-full">
                <Clock className="w-3 h-3" />
                <span>
                  {new Date(message.timestamp).toLocaleString(locale, {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'UTC' // 显示UTC时间，避免时区转换
                  })}
                </span>
              </div>
            </div>
            <div className="text-sm text-muted-foreground line-clamp-3 leading-relaxed bg-white/40 dark:bg-black/10 rounded-lg p-3">
              {message.content.substring(0, 150)}...
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDocument?.(message.content)}
              className="text-xs h-8 rounded-xl bg-white/60 dark:bg-black/20 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all duration-200"
            >
              <FileText className="w-3 h-3 mr-1.5" />
              {t('buttons.view_version')}
            </Button>
          </div>
        ) : (
          <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
        )}
      </div>
    </div>
  );
}
