import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations();

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/terms-of-service`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/terms-of-service`;
  }

  return {
    title: t("legal.terms_of_service.title", { default: "Terms of Service" }),
    description: t("legal.terms_of_service.description", { 
      default: "Terms of Service for GTPlanner - AI-powered planning tool for developers" 
    }),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function TermsOfServicePage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Back Button */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100">
            <Link href={`/${locale === "en" ? "" : locale}`} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("common.back", { default: "Back" })}
            </Link>
          </Button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t("legal.terms_of_service.title")}
          </h1>
          <div className="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
        </div>

        {/* Content Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12">
          <div className="prose prose-lg dark:prose-invert max-w-none">

            {/* Introduction */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.introduction.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {t("legal.terms_of_service.introduction.content")}
              </p>
            </section>

            {/* Use of Service */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.use_of_service.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {t("legal.terms_of_service.use_of_service.content")}
              </p>
            </section>

            {/* User Accounts */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.user_accounts.title")}
              </h2>
              <div className="space-y-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {t("legal.terms_of_service.user_accounts.account_creation.title")}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {t("legal.terms_of_service.user_accounts.account_creation.content")}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {t("legal.terms_of_service.user_accounts.account_security.title")}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {t("legal.terms_of_service.user_accounts.account_security.content")}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {t("legal.terms_of_service.user_accounts.user_responsibilities.title")}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {t("legal.terms_of_service.user_accounts.user_responsibilities.content")}
                  </p>
                </div>
              </div>
            </section>

            {/* Intellectual Property */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.intellectual_property.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.terms_of_service.intellectual_property.content")}
              </p>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  {t("legal.terms_of_service.intellectual_property.ownership")}
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  {t("legal.terms_of_service.intellectual_property.user_content")}
                </li>
              </ul>
            </section>

            {/* Prohibited Activities */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.prohibited_activities.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.terms_of_service.prohibited_activities.intro")}
              </p>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                {[0, 1, 2, 3, 4].map((index) => {
                  try {
                    const item = t(`legal.terms_of_service.prohibited_activities.items.${index}`);
                    return (
                      <li key={index} className="flex items-start">
                        <span className="text-red-500 mr-2">•</span>
                        {item}
                      </li>
                    );
                  } catch (error) {
                    return null;
                  }
                })}
              </ul>
            </section>

            {/* Privacy and Data Collection */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.privacy.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {t("legal.terms_of_service.privacy.intro")}
              </p>
              <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.terms_of_service.privacy.account_info")}</strong>: {t("legal.terms_of_service.privacy.account_info_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.terms_of_service.privacy.usage_details")}</strong>: {t("legal.terms_of_service.privacy.usage_details_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.terms_of_service.privacy.device_info")}</strong>: {t("legal.terms_of_service.privacy.device_info_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.terms_of_service.privacy.cookies")}</strong>: {t("legal.terms_of_service.privacy.cookies_desc")}
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2 mt-1">•</span>
                  <div>
                    <strong>{t("legal.terms_of_service.privacy.payment_info")}</strong>: {t("legal.terms_of_service.privacy.payment_info_desc")}
                  </div>
                </li>
              </ul>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mt-4">
                {t("legal.terms_of_service.privacy.policy_link")}{" "}
                <a
                  href={`/${locale === "en" ? "" : locale + "/"}privacy-policy`}
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline font-medium"
                >
                  {t("legal.privacy_policy.title")}
                </a>.
              </p>
            </section>

            {/* Pricing and Payments */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.pricing.title")}
              </h2>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">•</span>
                  {t("legal.terms_of_service.pricing.final_purchases")}
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">•</span>
                  {t("legal.terms_of_service.pricing.price_changes")}
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">•</span>
                  {t("legal.terms_of_service.pricing.payment_agreement")}
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">•</span>
                  {t("legal.terms_of_service.pricing.payment_terms")}
                </li>
              </ul>
            </section>

            {/* Additional Sections */}
            {[
              "termination",
              "disclaimer",
              "limitation",
              "indemnification",
              "governing_law",
              "changes"
            ].map((key) => (
              <section key={key} className="mb-10">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                  {t(`legal.terms_of_service.${key}.title`)}
                </h2>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {t(`legal.terms_of_service.${key}.content`)}
                </p>
              </section>
            ))}

            {/* Contact Information */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                {t("legal.terms_of_service.contact.title")}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {t("legal.terms_of_service.contact.content")}{" "}
                <a
                  href={`mailto:${t("common.support_email")}`}
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline font-medium"
                >
                  {t("common.support_email")}
                </a>.
              </p>
            </section>

            {/* Footer */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mt-12">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center leading-relaxed">
                  {t("legal.terms_of_service.acknowledgment")}
                </p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
