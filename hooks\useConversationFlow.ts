import { ChatState } from '@/types/conversation';
import { Message } from './useChatState';
import { callStreamConversation, formatConversationHistory, extractCurrentPlan, extractCurrentDocument, StreamCallbacks } from '@/lib/conversation-api';

export interface ConversationFlowHook {
  handleUnifiedConversationFlow: (
    input: string,
    sessionId?: string,
    messages?: Message[],
    shortPlanResult?: string,
    longDocResult?: string,
    locale?: string,
    callbacks?: {
      setState: (state: ChatState) => void;
      setLoadingStartTime: (time: number) => void;
      setStreamingMessage: (message: string) => void;
      setStreamingPlan: (plan: string) => void;
      setStreamingDoc: (doc: string) => void;
      setStreamingAnalysis: (analysis: string) => void;
      setShortPlanResult: (result: string) => void;
      setEditablePlan: (plan: string) => void;
      setLongDocResult: (result: string) => void;
      setEditableDoc: (doc: string) => void;
      setShowDocPanel: (show: boolean) => void;
      addMessage: (content: string, role: 'user' | 'assistant', type?: 'plan' | 'message' | 'document' | 'analysis', sessionId?: string) => Promise<void>;
      getLocalizedError: (error: string) => string;
    }
  ) => Promise<void>;

  handleGenerateDocument: (
    shortPlanResult: string,
    currentSessionId: string | null,
    messages: Message[],
    longDocResult: string,
    locale: string,
    callbacks: {
      setState: (state: ChatState) => void;
      setStreamingMessage: (message: string) => void;
      setStreamingDoc: (doc: string) => void;
      setStreamingAnalysis: (analysis: string) => void;
      setLongDocResult: (result: string) => void;
      setEditableDoc: (doc: string) => void;
      setShowDocPanel: (show: boolean) => void;
      addMessage: (content: string, role: 'user' | 'assistant', type?: 'plan' | 'message' | 'document' | 'analysis', sessionId?: string) => Promise<void>;
    }
  ) => Promise<void>;

  handleStreamConversation: (
    message: string,
    conversationHistory: any[],
    sessionId?: string,
    currentPlan?: string,
    currentDocument?: string,
    callbacks?: StreamCallbacks,
    action?: 'generate_document',
    language?: string
  ) => Promise<void>;
}

export function useConversationFlow(): ConversationFlowHook {
  // 流式对话处理函数
  const handleStreamConversation = async (
    message: string,
    conversationHistory: any[],
    sessionId?: string,
    currentPlan?: string,
    currentDocument?: string,
    callbacks?: StreamCallbacks,
    action?: 'generate_document',
    language?: string
  ) => {
    try {
      const formattedHistory = formatConversationHistory(conversationHistory);

      const context = {
        current_plan: currentPlan || extractCurrentPlan(conversationHistory),
        current_document: currentDocument || extractCurrentDocument(conversationHistory)
      };

      await callStreamConversation(
        message,
        formattedHistory,
        sessionId,
        context,
        callbacks,
        action,
        language
      );

    } catch (error) {
      console.error('Stream conversation error:', error);
      callbacks?.onError?.(error instanceof Error ? error.message : "未知错误");
    }
  };

  // 处理流式对话
  const handleUnifiedConversationFlow = async (
    input: string,
    sessionId?: string,
    messages: Message[] = [],
    shortPlanResult?: string,
    longDocResult?: string,
    locale?: string,
    callbacks?: {
      setState: (state: ChatState) => void;
      setLoadingStartTime: (time: number) => void;
      setStreamingMessage: (message: string) => void;
      setStreamingPlan: (plan: string) => void;
      setStreamingDoc: (doc: string) => void;
      setStreamingAnalysis: (analysis: string) => void;
      setShortPlanResult: (result: string) => void;
      setEditablePlan: (plan: string) => void;
      setLongDocResult: (result: string) => void;
      setEditableDoc: (doc: string) => void;
      setShowDocPanel: (show: boolean) => void;
      addMessage: (content: string, role: 'user' | 'assistant', type?: 'plan' | 'message' | 'document' | 'analysis', sessionId?: string) => Promise<void>;
      getLocalizedError: (error: string) => string;
    }
  ) => {
    if (!callbacks) return;

    try {
      callbacks.setState(ChatState.LOADING);
      callbacks.setLoadingStartTime(Date.now());
      callbacks.setStreamingMessage("");

      let currentAssistantMessage = "";
      let currentPlanContent = "";
      let currentDocContent = "";
      let currentAnalysisContent = "";
      let finalState = ChatState.CONVERSATION; // 默认状态

      // 重置流式状态
      callbacks.setStreamingMessage("");
      callbacks.setStreamingPlan("");
      callbacks.setStreamingDoc("");
      callbacks.setStreamingAnalysis("");

      const streamCallbacks: StreamCallbacks = {
        onText: (text: string) => {
          // 累积增量内容进行流式显示
          currentAssistantMessage += text;
          callbacks.setStreamingMessage(currentAssistantMessage);
        },

        onShortPlan: (plan: string) => {
          // 累积规划内容进行流式显示
          console.log('onShortPlan called with:', JSON.stringify(plan));
          currentPlanContent += plan;
          callbacks.setStreamingPlan(currentPlanContent);
          callbacks.setShortPlanResult(currentPlanContent);
          callbacks.setEditablePlan(currentPlanContent);
        },

        onAnalysis: (analysis: string) => {
          // 如果是第一次接收到分析内容，清空之前的文档状态并打开文档面板
          if (currentAnalysisContent === "") {
            callbacks.setLongDocResult("");
            callbacks.setEditableDoc("");
            callbacks.setStreamingDoc("");
            // 在分析阶段就打开文档面板，确保用户能看到思考过程
            callbacks.setShowDocPanel(true);
          }

          // 累积需求分析内容进行流式显示
          currentAnalysisContent += analysis;
          // 将分析内容设置到专门的状态中，用于AnalysisPanel显示
          callbacks.setStreamingAnalysis(currentAnalysisContent);
        },

        onLongDoc: (doc: string) => {
          // 如果是第一次接收到文档内容，且没有分析内容，清空之前的文档状态
          if (currentDocContent === "" && currentAnalysisContent === "") {
            callbacks.setLongDocResult("");
            callbacks.setEditableDoc("");
            callbacks.setStreamingDoc("");
          }

          // 累积文档内容进行流式显示
          currentDocContent += doc;
          callbacks.setStreamingDoc(currentDocContent);
          callbacks.setLongDocResult(currentDocContent);
          callbacks.setEditableDoc(currentDocContent);
          callbacks.setShowDocPanel(true);
        },

        onTagEnd: (tagName: string, content: string) => {
          console.log('Tag ended:', tagName, 'content length:', content.length);
          // 根据标签结束来设置最终状态
          if (tagName === 'SHORT_PLAN' && content.trim()) {
            finalState = ChatState.SHORT_CONFIRM;
            console.log('State will be set to SHORT_CONFIRM due to SHORT_PLAN tag end');
          } else if (tagName === 'LONG_DOC' && content.trim()) {
            finalState = ChatState.LONG_RESULT;
            console.log('State will be set to LONG_RESULT due to LONG_DOC tag end');
          }
        },

        onError: (error: string) => {
          console.error('Stream error:', error);
          // 使用统一的错误恢复处理
          callbacks.setStreamingMessage("");
          callbacks.setStreamingPlan("");
          callbacks.setStreamingDoc("");
          callbacks.setStreamingAnalysis("");
          callbacks.setLoadingStartTime(0);
          callbacks.setState(ChatState.CONVERSATION);
          // 添加错误消息
          callbacks.addMessage(`❌ ${error}`, 'assistant', 'message', sessionId);
        },

        onComplete: async () => {
          console.log('onComplete called:', {
            currentAssistantMessage: currentAssistantMessage.length,
            currentAnalysisContent: currentAnalysisContent.length,
            currentPlanContent: currentPlanContent.length,
            currentDocContent: currentDocContent.length,
            finalState
          });

          // 保存消息到历史
          if (currentAssistantMessage) {
            await callbacks.addMessage(currentAssistantMessage, 'assistant', 'message', sessionId);
          }

          if (currentAnalysisContent) {
            // 保存需求分析内容为analysis类型
            await callbacks.addMessage(currentAnalysisContent, 'assistant', 'analysis', sessionId);
          }

          if (currentPlanContent) {
            await callbacks.addMessage(currentPlanContent, 'assistant', 'plan', sessionId);
          }

          if (currentDocContent) {
            await callbacks.addMessage(currentDocContent, 'assistant', 'document', sessionId);
          }

          // 设置最终状态（在标签结束时已确定）
          console.log('Setting final state to:', finalState);
          callbacks.setState(finalState);

          callbacks.setStreamingMessage("");

          // 延迟清空分析流式状态，让AnalysisPanel有时间触发自动收起逻辑
          if (currentAnalysisContent) {
            setTimeout(() => {
              callbacks.setStreamingAnalysis("");
            }, 100); // 100ms延迟，确保消息历史更新完成
          } else {
            callbacks.setStreamingAnalysis("");
          }
        }
      };

      // 调用流式对话接口
      await handleStreamConversation(
        input,
        messages,
        sessionId,
        shortPlanResult,
        longDocResult,
        streamCallbacks,
        undefined, // action
        locale // 传入语言参数
      );

    } catch (error) {
      console.error('Stream conversation failed:', error);
      // 使用统一的错误恢复处理
      callbacks.setStreamingMessage("");
      callbacks.setStreamingPlan("");
      callbacks.setStreamingDoc("");
      callbacks.setStreamingAnalysis("");
      callbacks.setLoadingStartTime(0);
      callbacks.setState(ChatState.CONVERSATION);
      // 出错时默认当作对话处理
      await callbacks.addMessage(callbacks.getLocalizedError("抱歉，我现在无法理解您的消息。请您再详细描述一下。"), 'assistant', 'message', sessionId);
    }
  };

  // 处理文档生成按钮点击
  const handleGenerateDocument = async (
    shortPlanResult: string,
    currentSessionId: string | null,
    messages: Message[],
    longDocResult: string,
    locale: string,
    callbacks: {
      setState: (state: ChatState) => void;
      setStreamingMessage: (message: string) => void;
      setStreamingDoc: (doc: string) => void;
      setStreamingAnalysis: (analysis: string) => void;
      setLongDocResult: (result: string) => void;
      setEditableDoc: (doc: string) => void;
      setShowDocPanel: (show: boolean) => void;
      addMessage: (content: string, role: 'user' | 'assistant', type?: 'plan' | 'message' | 'document' | 'analysis', sessionId?: string) => Promise<void>;
    }
  ) => {
    if (!shortPlanResult) return;

    const documentRequest = "请基于当前规划生成详细的设计文档";

    // 直接调用流式对话，指定action为generate_document
    try {
      // 设置长文档加载状态，禁用按钮
      callbacks.setState(ChatState.LONG_LOADING);
      callbacks.setStreamingMessage("");
      callbacks.setStreamingDoc("");
      callbacks.setLongDocResult(""); // 清空之前的长文档结果
      callbacks.setEditableDoc(""); // 清空可编辑文档内容

      let currentDocContent = "";
      let currentAnalysisContent = "";
      let finalState = ChatState.CONVERSATION; // 默认状态

      const streamCallbacks: StreamCallbacks = {
        onText: (text: string) => {
          // 对于文档生成，完全忽略TEXT标签内容
          // 所有内容都应该通过LONG_DOC或ANALYSIS标签处理
          console.log('Document generation - ignoring TEXT content:', JSON.stringify(text));
          // 不设置streamingMessage，确保不在对话记录中显示
          // 注意：这里不调用setStreamingMessage，避免在聊天记录中显示重复内容
        },

        onAnalysis: (analysis: string) => {
          // 如果是第一次接收到分析内容，清空之前的文档状态并打开文档面板
          if (currentAnalysisContent === "") {
            callbacks.setLongDocResult("");
            callbacks.setEditableDoc("");
            callbacks.setStreamingDoc("");
            // 在分析阶段就打开文档面板，这样分析内容可以在正确的位置显示
            callbacks.setShowDocPanel(true);
          }

          // 累积需求分析内容进行流式显示
          currentAnalysisContent += analysis;
          // 将分析内容设置到专门的状态中，用于AnalysisPanel显示
          callbacks.setStreamingAnalysis(currentAnalysisContent);
        },

        onLongDoc: (doc: string) => {
          // 如果是第一次接收到文档内容，且没有分析内容，清空之前的文档状态
          if (currentDocContent === "" && currentAnalysisContent === "") {
            callbacks.setLongDocResult("");
            callbacks.setEditableDoc("");
            callbacks.setStreamingDoc("");
          }

          // 累积文档内容进行流式显示
          console.log('Document generation - LONG_DOC content:', JSON.stringify(doc));
          currentDocContent += doc;
          callbacks.setStreamingDoc(currentDocContent);
          callbacks.setLongDocResult(currentDocContent);
          callbacks.setEditableDoc(currentDocContent);
          callbacks.setShowDocPanel(true);
        },

        onTagEnd: (tagName: string, content: string) => {
          console.log('Document generation - Tag ended:', tagName, 'content length:', content.length);
          // 根据标签结束来设置最终状态
          if (tagName === 'LONG_DOC' && content.trim()) {
            finalState = ChatState.LONG_RESULT;
            console.log('Document generation - State will be set to LONG_RESULT due to LONG_DOC tag end');
          } else if (tagName === 'ANALYSIS' && content.trim()) {
            // ANALYSIS标签结束时，立即清空流式分析状态，触发自动收起
            console.log('Document generation - ANALYSIS tag ended, clearing streamingAnalysis');
            callbacks.setStreamingAnalysis("");
            console.log('Document generation - Waiting for LONG_DOC');
          }
        },

        onError: (error: string) => {
          console.error('Stream error:', error);
          // 使用统一的错误恢复处理
          callbacks.setStreamingMessage("");
          callbacks.setStreamingDoc("");
          callbacks.setStreamingAnalysis("");
          callbacks.setState(ChatState.CONVERSATION);
          // 添加错误消息
          callbacks.addMessage(`❌ ${error}`, 'assistant', 'message', currentSessionId || undefined);
        },

        onComplete: async () => {
          console.log('Document generation onComplete called:', {
            currentAnalysisContent: currentAnalysisContent.length,
            currentDocContent: currentDocContent.length,
            finalState
          });

          // 保存消息到历史
          if (currentAnalysisContent) {
            // 保存需求分析内容为analysis类型
            await callbacks.addMessage(currentAnalysisContent, 'assistant', 'analysis', currentSessionId || undefined);
          }

          if (currentDocContent) {
            await callbacks.addMessage(currentDocContent, 'assistant', 'document', currentSessionId || undefined);
          }

          // 设置最终状态（在标签结束时已确定）
          console.log('Document generation - Setting final state to:', finalState);
          callbacks.setState(finalState);

          callbacks.setStreamingMessage("");
          callbacks.setStreamingDoc("");

          // streamingAnalysis已经在ANALYSIS标签结束时清空，这里不需要再次清空
          console.log('Document generation onComplete - streamingAnalysis should already be cleared');
        }
      };

      // 调用流式对话接口，指定action
      await handleStreamConversation(
        documentRequest,
        messages,
        currentSessionId || undefined,
        shortPlanResult,
        longDocResult,
        streamCallbacks,
        'generate_document', // 明确指定操作类型
        locale // 传入语言参数
      );

    } catch (error) {
      console.error('Document generation failed:', error);
      callbacks.setState(ChatState.CONVERSATION);
      callbacks.setStreamingMessage("");
      callbacks.setStreamingDoc("");
      callbacks.setStreamingAnalysis("");
    }
  };

  return {
    handleUnifiedConversationFlow,
    handleGenerateDocument,
    handleStreamConversation,
  };
}
