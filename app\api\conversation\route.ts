import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';

const baseUrl = process.env.SHORT_PLANNING_BASEURL;
const unifiedUrl = `${baseUrl}/chat/unified`;

export async function POST(req: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.api_key) {
      console.log("userInfo.api_key is empty")
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }

    const body = await req.json();
    console.log(`userInfo.api_key: ${userInfo.api_key}`)

    // 完全使用流式响应
    const response = await fetch(unifiedUrl, {
      method: 'POST',
      headers: {
        'accept': 'text/plain',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userInfo.api_key}`,
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        const reader = response.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              controller.close();
              break;
            }
            controller.enqueue(value);
          }
        } catch (error) {
          controller.error(error);
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error('Unified conversation API error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
