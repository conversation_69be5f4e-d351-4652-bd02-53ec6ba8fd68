"use client";

import { useTranslations } from 'next-intl';
import { MessageList } from '@/components/chat/messages/message-list';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Share2, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import { useState } from 'react';
import { DesktopDocumentPanel } from '@/components/chat/document/desktop-document-panel';
import { MobileDocumentPanel } from '@/components/chat/document/mobile-document-panel';
import { ChatState } from '@/types/conversation';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { copyToClipboard, showSelectableText } from '@/lib/copy-to-clipboard';
import { cn } from '@/lib/utils';

interface SessionData {
  status: 'success';
  session: {
    id: number;
    uuid: string;
    title: string;
    created_at: Date | null;
    updated_at: Date | null;
    user_uuid: string;
    is_public: boolean;
  };
  user: {
    nickname: string | null;
    avatar_url: string | null;
  } | null;
  messages: Array<{
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: number;
    type: 'message' | 'plan' | 'document' | 'analysis';
  }>;
}

interface ShareChatClientProps {
  sessionData: SessionData;
  sessionId: string;
}

export default function ShareChatClient({ sessionData, sessionId }: ShareChatClientProps) {
  const t = useTranslations('chat');
  const shareT = useTranslations('Share.page');
  const isMobile = useMediaQuery("(max-width: 768px)");

  const { session, user, messages } = sessionData;

  // 设计文档相关状态
  const [showDocPanel, setShowDocPanel] = useState(false);
  const [documentContent, setDocumentContent] = useState('');
  const [isMarkdownPreview, setIsMarkdownPreview] = useState(true);

  // 复制分享链接
  const handleShare = async () => {
    const shareUrl = `${window.location.origin}/share/${sessionId}`;

    const success = await copyToClipboard(shareUrl);

    if (success) {
      toast.success(shareT('copy_success'));
    } else {
      toast.error(shareT('copy_failed'));
      // 显示可选中的链接文本
      showSelectableText(shareUrl);
    }
  };

  // 前往应用
  const handleGoToApp = () => {
    window.open(window.location.origin, '_blank');
  };

  // 查看设计文档
  const handleViewDocument = (content: string) => {
    setDocumentContent(content);
    setShowDocPanel(true);
    setIsMarkdownPreview(true); // 分享页面默认显示预览模式
  };

  // 关闭设计文档面板
  const handleCloseDocPanel = () => {
    setShowDocPanel(false);
    setDocumentContent('');
  };

  return (
    <div className="h-screen bg-gradient-to-br from-background via-background to-muted/20 flex flex-col">
      {/* 头部 - 固定高度 */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user?.avatar_url || undefined} />
              <AvatarFallback>
                {user?.nickname?.[0]?.toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="font-semibold text-lg">{session.title}</h1>
              <p className="text-sm text-muted-foreground">
                {shareT('shared_by', { user: user?.nickname || shareT('default_user') })}
              </p>
            </div>
            <Badge variant="secondary" className="ml-2">
              <Share2 className="w-3 h-3 mr-1" />
              {shareT('public_badge')}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="hidden sm:flex"
            >
              <Share2 className="w-4 h-4 mr-2" />
              {shareT('copy_link')}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleGoToApp}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              {shareT('go_to_app')}
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域：flex布局实现1:2比例 */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* 对话内容区域 - 1/3 宽度 */}
        <div className={cn(
          "flex flex-col transition-all duration-800 ease-in-out relative min-w-0",
          showDocPanel && !isMobile ? "flex-[1]" : "flex-1"
        )}>
          <div className="flex-1 overflow-auto chat-scroll-area">
            <div className="px-4 py-6">
              {messages.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <p>这个对话还没有任何消息</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <MessageList
                    messages={messages}
                    showPlans={true}
                    locale="zh"
                    t={t}
                    onViewDocument={handleViewDocument}
                    isSharedView={true}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 底部提示 */}
          <div className="border-t bg-muted/50 px-4 py-3">
            <div className="px-2">
              <p className="text-sm text-muted-foreground text-center">
                {shareT('footer_message')}
                <Button
                  variant="link"
                  size="sm"
                  onClick={handleGoToApp}
                  className="ml-1 p-0 h-auto text-sm text-primary"
                >
                  {shareT('footer_link')}
                </Button>
              </p>
            </div>
          </div>
        </div>

        {/* 桌面端设计文档面板 - 2/3 宽度 */}
        {!isMobile && (
          <DesktopDocumentPanel
            show={showDocPanel}
            content={documentContent}
            onChange={() => {}} // 分享页面不允许编辑
            isMarkdownPreview={isMarkdownPreview}
            onTogglePreview={setIsMarkdownPreview}
            onClose={handleCloseDocPanel}
            isViewingHistory={true} // 分享页面总是查看历史
            onBackToProgress={() => {}} // 分享页面不需要此功能
            state={ChatState.LONG_RESULT} // 分享页面总是完成状态
            t={t}
          />
        )}
      </div>

      {/* 移动端设计文档面板 */}
      {isMobile && (
        <MobileDocumentPanel
          show={showDocPanel}
          content={documentContent}
          onChange={() => {}} // 分享页面不允许编辑
          isMarkdownPreview={isMarkdownPreview}
          onTogglePreview={setIsMarkdownPreview}
          onClose={handleCloseDocPanel}
          isViewingHistory={true} // 分享页面总是查看历史
          onBackToProgress={() => {}} // 分享页面不需要此功能
          state={ChatState.LONG_RESULT} // 分享页面总是完成状态
          t={t}
        />
      )}

      {/* 移动端分享按钮 */}
      {!showDocPanel && (
        <div className="sm:hidden fixed bottom-4 right-4">
          <Button
            size="sm"
            onClick={handleShare}
            className="rounded-full shadow-lg"
          >
            <Share2 className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
}