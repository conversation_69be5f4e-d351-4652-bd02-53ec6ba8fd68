import { LandingPage } from "@/types/pages/landing";
import { getLandingPageData } from "@/lib/config/landing-data";

export async function getLandingPage(locale: string): Promise<LandingPage> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }

    // Use dynamic configuration with environment variables
    const dynamicData = getLandingPageData(locale);

    // For sections that don't have URLs to replace, fall back to JSON files
    const staticData = await import(
      `@/i18n/pages/landing/${locale.toLowerCase()}.json`
    ).then((module) => module.default);

    // Merge dynamic data (with env vars) with static data
    return {
      ...staticData,
      header: dynamicData.header,
      hero: dynamicData.hero,
      footer: dynamicData.footer,
    } as LandingPage;
  } catch (error) {
    console.warn(`Failed to load ${locale}.json, falling back to en.json`);
    const staticData = await import("@/i18n/pages/landing/en.json").then(
      (module) => module.default as LandingPage
    );

    const dynamicData = getLandingPageData("en");

    return {
      ...staticData,
      header: dynamicData.header,
      hero: dynamicData.hero,
      footer: dynamicData.footer,
    } as LandingPage;
  }
}
