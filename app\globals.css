@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);


  }
  .dark {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);


  }
}

@layer components {

  /* Chat page specific animations */
  .chat-transition-left {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .chat-panel-enter {
    animation: panelScaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .chat-panel-exit {
    animation: panelScaleOut 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes panelScaleIn {
    from {
      transform: scale(0.85);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes panelScaleOut {
    from {
      transform: scale(1);
      opacity: 1;
    }
    to {
      transform: scale(0.85);
      opacity: 0;
    }
  }

  /* Fixed bottom input */
  .chat-input-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 40;
  }

  /* Content padding to account for fixed input */
  .chat-content-with-input {
    padding-bottom: 120px;
  }

  /* Custom scrollbar styles for chat areas */
  .chat-scroll-area {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border) / 0.6) transparent;
  }

  .chat-scroll-area::-webkit-scrollbar {
    width: 8px;
  }

  .chat-scroll-area::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .chat-scroll-area::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border) / 0.6);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  .chat-scroll-area::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--border) / 0.8);
  }

  /* Session list specific scrollbar */
  .session-list-scroll {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .session-list-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .session-list-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .session-list-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  .session-list-scroll::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.5);
  }

  /* Document panel specific scrollbar */
  .doc-scroll-area {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .doc-scroll-area::-webkit-scrollbar {
    width: 8px;
  }

  .doc-scroll-area::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.1);
    border-radius: 4px;
  }

  .doc-scroll-area::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  .doc-scroll-area::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.5);
  }

  /* Enhanced message animations */
  .message-enter {
    animation: messageSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes messageSlideIn {
    from {
      opacity: 0;
      transform: translateY(10px) scale(0.98);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Improved card hover effects */
  .card-hover-lift {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px hsl(var(--foreground) / 0.1);
  }

  /* Gradient text effects */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Enhanced button animations */
  .button-scale {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .button-scale:hover {
    transform: scale(1.05);
  }

  .button-scale:active {
    transform: scale(0.95);
  }

  /* Improved focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/30 focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Mermaid diagram container styles */
  .mermaid-container {
    @apply flex justify-center items-center p-4 bg-muted/20 rounded-xl border border-border/30 my-4;
  }

  .mermaid-container svg {
    @apply max-w-full h-auto;
  }

  .mermaid-error {
    @apply bg-destructive/10 border border-destructive/30 rounded-xl p-4 my-4;
  }

  .mermaid-error p {
    @apply text-destructive font-medium mb-2;
  }

  .mermaid-error pre {
    @apply bg-destructive/5 border border-destructive/20 rounded-lg p-3 text-sm;
  }
}

@layer utilities {
  /* Utility classes for common patterns */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }
}
