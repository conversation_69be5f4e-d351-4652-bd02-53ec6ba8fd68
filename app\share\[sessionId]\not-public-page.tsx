"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { LockKeyhole } from "lucide-react";
import { useTranslations } from "next-intl";

interface NotPublicPageProps {
  sessionTitle: string;
}

export function NotPublicPage({ sessionTitle }: NotPublicPageProps) {
  const t = useTranslations('Share');

  const handleGoToApp = () => {
    window.open(window.location.origin, '_blank');
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
            <LockKeyhole className="h-8 w-8 text-muted-foreground" />
          </div>
          <CardTitle className="text-xl">{t('not_public.title')}</CardTitle>
          <CardDescription>
            {sessionTitle}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground">
            {t('not_public.description')}
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={handleGoToApp}>
            {t('not_public.go_to_app')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
