import { Credits<PERSON><PERSON>, CreditsTransType } from "./credit";
import { findUserByEmail, findUserByUuid, insertUser, updateUser<PERSON>pi<PERSON><PERSON> } from "@/models/user";

import { User } from "@/types/user";
import { auth } from "@/auth";
import { getOneYearLaterTimestr } from "@/lib/time";
import { getUserUuidByApiKey } from "@/models/apikey";
import { headers } from "next/headers";
import { increaseCredits } from "./credit";
import { HigressConsoleAPI } from "higress-console-api";

export async function saveUser(user: User) {
  try {
    const api = new HigressConsoleAPI({
      baseUrl: process.env.HIGRESS_CONSOLE_URL || '',
      username: process.env.HIGRESS_USERNAME || '',
      password: process.env.HIGRESS_PASSWORD || '',
    });


    const result = await api.bindConsumerToRoute("gt-planner", user.email);
    const token = result.token;
    if (typeof token !== 'string') {
      throw new Error(`Token ${token} is error`);
    }
    user.api_key = token;
    const existUser = await findUserByEmail(user.email);
    if (!existUser) {
      // Ensure user.uuid is not undefined
      if (!user.uuid) {
        throw new Error("User UUID is required");
      }
      // Convert User type to database insert type
      const userToInsert = {
        uuid: user.uuid,
        email: user.email,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        locale: user.locale,
        signin_type: user.signin_type,
        signin_ip: user.signin_ip,
        signin_provider: user.signin_provider,
        signin_openid: user.signin_openid,
        invite_code: user.invite_code,
        invited_by: user.invited_by,
        is_affiliate: user.is_affiliate,
        api_key: user.api_key,
        created_at: user.created_at ? new Date(user.created_at) : undefined,
      };
      await insertUser(userToInsert);
      // increase credits for new user, expire in one year
      await increaseCredits({
        user_uuid: user.uuid,
        trans_type: CreditsTransType.NewUser,
        credits: CreditsAmount.NewUserGet,
        expired_at: getOneYearLaterTimestr(),
      });
    } else {
      user.id = existUser.id;
      user.uuid = existUser.uuid;
      user.created_at = existUser.created_at ? existUser.created_at.toISOString() : undefined;
      await updateUserApiKey(user.uuid || "", token);
    }

    return user;
  } catch (e) {
    console.log("save user failed: ", e);
    throw e;
  }
}

export async function getUserUuid() {
  let user_uuid = "";

  const token = getBearerToken();

  if (token) {
    // api key
    if (token.startsWith("sk-")) {
      const user_uuid = await getUserUuidByApiKey(token);

      return user_uuid || "";
    }
  }

  const session = await auth();
  if (session && session.user && session.user.uuid) {
    user_uuid = session.user.uuid;
  }

  return user_uuid;
}

export function getBearerToken() {
  const h = headers();
  const auth = h.get("Authorization");
  if (!auth) {
    return "";
  }

  return auth.replace("Bearer ", "");
}

export async function getUserEmail() {
  let user_email = "";

  const session = await auth();
  if (session && session.user && session.user.email) {
    user_email = session.user.email;
  }

  return user_email;
}

export async function getUserInfo() {
  let user_uuid = await getUserUuid();

  if (!user_uuid) {
    return;
  }

  const user = await findUserByUuid(user_uuid);

  return user;
}

export async function isUserAdmin(userEmail?: string): Promise<boolean> {
  if (!userEmail) {
    const user = await getUserInfo();
    if (!user?.email) {
      return false;
    }
    userEmail = user.email;
  }

  const adminEmails = process.env.ADMIN_EMAILS?.split(",") || [];
  return adminEmails.includes(userEmail);
}
