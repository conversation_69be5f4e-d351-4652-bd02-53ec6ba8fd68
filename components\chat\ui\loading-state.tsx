"use client";
import { Timer } from "./timer";
import { DynamicProgressBar } from "./progress-bars";

interface LoadingStateProps {
  message: string;
  startTime: number;
  t: any;
}

export function LoadingState({ message, startTime, t }: LoadingStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
      <p className="text-lg font-medium">{message}</p>
      <Timer startTime={startTime} t={t} />
      <DynamicProgressBar startTime={startTime} />
    </div>
  );
}
