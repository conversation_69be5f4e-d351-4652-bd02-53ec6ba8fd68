import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';
import { redirect } from 'next/navigation';

const baseUrl = process.env.SHORT_PLANNING_BASEURL;
const url = `${baseUrl}/planning/short`;


export async function POST(req: NextRequest) {
  console.log("-------")
  console.log(req)
  const userInfo = await getUserInfo();
  if (!userInfo?.api_key) {
    console.log("userInfo.api_key is empty")
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
  try {
    const body = await req.json();
    console.log(`userInfo.api_key: ${userInfo.api_key}`)
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userInfo.api_key}`,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
