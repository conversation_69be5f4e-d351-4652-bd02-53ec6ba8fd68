import { ConversationRequest, ConversationResponse, ChatMessage } from '@/types/conversation';

// 流式响应回调函数类型
export interface StreamCallbacks {
  onText?: (text: string) => void;
  onShortPlan?: (plan: string) => void;
  onLongDoc?: (doc: string) => void;
  onAnalysis?: (analysis: string) => void; // 需求分析流式输出
  onError?: (error: string) => void;
  onComplete?: () => void;
  // 标签结束时的回调，用于状态设置
  onTagEnd?: (tagName: string, content: string) => void;
}

// 重试配置
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1秒
  maxDelay: 10000, // 10秒
  backoffFactor: 2
};

// 判断是否为可重试的错误
function isRetriableError(error: any): boolean {
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return true; // 网络连接错误
  }
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return message.includes('network') ||
           message.includes('timeout') ||
           message.includes('connection') ||
           message.includes('fetch');
  }
  return false;
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 计算重试延迟时间
function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
  return Math.min(delay, config.maxDelay);
}

// 标签解析器类
export class TagParser {
  private buffer: string = '';
  private currentTag: string | null = null;
  private currentContent: string = '';
  private lastSentLength: number = 0; // 记录上次发送的内容长度
  private callbacks: StreamCallbacks;

  constructor(callbacks: StreamCallbacks) {
    this.callbacks = callbacks;
  }

  processChunk(chunk: string) {
    this.buffer += chunk;
    this.parseBuffer();
  }

  private parseBuffer() {
    // 直接处理缓冲区内容，查找标签
    this.processContent();
  }

  private processContent() {
    let processedLength = 0;

    while (processedLength < this.buffer.length) {
      const remainingBuffer = this.buffer.substring(processedLength);

      // 检查是否包含完整的标签对（开始和结束在同一段内容中）
      // 支持 [TAG_END] 和 [/TAG_END] 两种结束标签格式
      const completeTagMatch = remainingBuffer.match(/\[(\w+)_START\](.*?)\[\/?(\w+)_END\]/);
      if (completeTagMatch) {
        const startTag = completeTagMatch[1];
        const content = completeTagMatch[2];
        const endTag = completeTagMatch[3];

        if (startTag === endTag) {
          // 处理标签前的内容
          const beforeTag = remainingBuffer.substring(0, completeTagMatch.index || 0);
          if (beforeTag && this.currentTag) {
            this.currentContent += beforeTag;
          } else if (beforeTag && !this.currentTag) {
            // 标签外的内容，忽略或处理为普通文本
          }

          // 处理完整的标签
          this.handleTagContent(startTag, content);
          
          // 调用onTagEnd回调，传递标签名和完整内容
          this.callbacks.onTagEnd?.(startTag, content);

          processedLength += (completeTagMatch.index || 0) + completeTagMatch[0].length;
          continue;
        }
      }

      // 检查开始标签
      const startTagMatch = remainingBuffer.match(/\[(\w+)_START\]/);
      if (startTagMatch && !this.currentTag) {
        const tagName = startTagMatch[1];

        // 处理标签前的内容
        const beforeTag = remainingBuffer.substring(0, startTagMatch.index || 0);
        if (beforeTag) {
          // 标签外的内容，可以作为普通文本处理
        }

        this.currentTag = tagName;
        this.currentContent = '';
        this.lastSentLength = 0; // 重置发送长度
        processedLength += (startTagMatch.index || 0) + startTagMatch[0].length;
        continue;
      }

      // 检查结束标签 - 支持 [TAG_END] 和 [/TAG_END] 两种格式
      const endTagMatch = remainingBuffer.match(/\[\/?(\w+)_END\]/);
      if (endTagMatch && this.currentTag === endTagMatch[1]) {
        // 处理标签内的内容
        const beforeEndTag = remainingBuffer.substring(0, endTagMatch.index || 0);
        this.currentContent += beforeEndTag;

        // 发送最后的增量内容（如果有的话）
        if (this.currentContent.length > this.lastSentLength) {
          const newContent = this.currentContent.substring(this.lastSentLength);
          this.handleTagContentIncremental(this.currentTag, newContent);
        }

        // 对于支持增量更新的标签，不需要再发送完整内容
        // 对于其他标签，在结束时发送完整内容
        const incrementalTags = ['TEXT', 'SHORT_PLAN', 'LONG_DOC', 'ANALYSIS'];
        if (!incrementalTags.includes(this.currentTag)) {
          this.handleTagContent(this.currentTag, this.currentContent);
        }

        // 调用onTagEnd回调，传递标签名和完整内容
        this.callbacks.onTagEnd?.(this.currentTag, this.currentContent);

        this.currentTag = null;
        this.currentContent = '';
        this.lastSentLength = 0; // 重置发送长度

        processedLength += (endTagMatch.index || 0) + endTagMatch[0].length;
        continue;
      }

      // 如果在标签内，累积所有剩余内容并实时更新
      if (this.currentTag) {
        this.currentContent += remainingBuffer;

        // 只发送新增的内容（增量更新）
        if (this.currentContent.length > this.lastSentLength) {
          const newContent = this.currentContent.substring(this.lastSentLength);
          this.handleTagContentIncremental(this.currentTag, newContent);
          this.lastSentLength = this.currentContent.length;
        }

        processedLength = this.buffer.length;
      } else {
        // 不在标签内，将内容当作TEXT处理
        if (remainingBuffer.trim()) {
          this.callbacks.onText?.(remainingBuffer);
        }
        processedLength = this.buffer.length;
      }
    }

    // 更新缓冲区，只保留未处理的部分
    this.buffer = this.buffer.substring(processedLength);
  }

  private handleTagContent(tag: string, content: string) {
    switch (tag) {
      case 'TEXT':
        this.callbacks.onText?.(content);
        break;
      case 'SHORT_PLAN':
        this.callbacks.onShortPlan?.(content);
        break;
      case 'LONG_DOC':
        this.callbacks.onLongDoc?.(content);
        break;
      case 'ANALYSIS':
        this.callbacks.onAnalysis?.(content);
        break;
      case 'ERROR':
        this.callbacks.onError?.(content);
        break;
      default:
        // 忽略未知标签
        console.warn(`Unknown tag: ${tag}`);
        break;
    }
  }

  private handleTagContentIncremental(tag: string, incrementalContent: string) {
    // 对于TEXT、SHORT_PLAN、LONG_DOC和ANALYSIS标签，发送真正的增量内容
    if (tag === 'TEXT') {
      this.callbacks.onText?.(incrementalContent);
    } else if (tag === 'SHORT_PLAN') {
      this.callbacks.onShortPlan?.(incrementalContent);
    } else if (tag === 'LONG_DOC') {
      this.callbacks.onLongDoc?.(incrementalContent);
    } else if (tag === 'ANALYSIS') {
      this.callbacks.onAnalysis?.(incrementalContent);
    }
    // 其他标签类型暂时不支持增量更新，等到完整内容时再处理
  }

  complete() {
    this.callbacks.onComplete?.();
  }
}

/**
 * 调用流式统一对话接口（带重试机制）
 */
export async function callStreamConversation(
  message: string,
  conversationHistory: ChatMessage[] = [],
  sessionId?: string,
  context?: {
    current_plan?: string;
    current_document?: string;
    user_intent_hint?: string;
  },
  callbacks?: StreamCallbacks,
  action?: 'generate_document',
  language?: string,
  retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      const requestBody: ConversationRequest = {
        message,
        conversation_history: conversationHistory,
        session_id: sessionId,
        language: language || 'zh',
        action: action,
        context: context || {}
      };

      const response = await fetch('/api/conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      const parser = new TagParser(callbacks || {});

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          // 移除 "data: " 前缀
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              // 将占位符替换回换行符，恢复markdown格式
              const content = line.substring(6).replace(/<\|newline\|>/g, '\n');
              parser.processChunk(content);
            }
          }
        }
      } finally {
        reader.releaseLock();
        parser.complete();
      }

      // 如果成功，直接返回
      return;

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      console.error(`Stream conversation API error (attempt ${attempt + 1}):`, lastError);

      // 如果是最后一次尝试，或者错误不可重试，直接抛出错误
      if (attempt === retryConfig.maxRetries || !isRetriableError(lastError)) {
        break;
      }

      // 计算延迟时间并等待
      const delayMs = calculateDelay(attempt, retryConfig);
      console.log(`Retrying in ${delayMs}ms... (attempt ${attempt + 1}/${retryConfig.maxRetries})`);
      await delay(delayMs);
    }
  }

  // 所有重试都失败了，调用错误回调
  const errorMessage = lastError?.message || "未知错误";
  console.error('All retry attempts failed:', errorMessage);
  callbacks?.onError?.(errorMessage);
}





/**
 * 调用统一对话接口（带重试机制）
 */
export async function callUnifiedConversation(
  message: string,
  conversationHistory: ChatMessage[] = [],
  sessionId?: string,
  context?: {
    current_plan?: string;
    current_document?: string;
    user_intent_hint?: string;
  },
  language?: string,
  retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<ConversationResponse> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      const requestBody: ConversationRequest = {
        message,
        conversation_history: conversationHistory,
        session_id: sessionId,
        language: language || 'zh',
        context: context || {}
      };

      const response = await fetch('/api/conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // 验证响应格式
      if (!data.success) {
        throw new Error(data.error || 'API调用失败');
      }

      return data as ConversationResponse;

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      console.error(`Unified conversation API error (attempt ${attempt + 1}):`, lastError);

      // 如果是最后一次尝试，或者错误不可重试，跳出循环
      if (attempt === retryConfig.maxRetries || !isRetriableError(lastError)) {
        break;
      }

      // 计算延迟时间并等待
      const delayMs = calculateDelay(attempt, retryConfig);
      console.log(`Retrying in ${delayMs}ms... (attempt ${attempt + 1}/${retryConfig.maxRetries})`);
      await delay(delayMs);
    }
  }

  // 所有重试都失败了，返回错误响应
  const errorMessage = lastError?.message || "未知错误";
  console.error('All retry attempts failed:', errorMessage);

  return {
    success: false,
    data: {
      response: "网络错误，请检查连接后重试。",
      intent: "conversation",
      confidence: 0.0,
      actions: [],
      metadata: {
        language: "zh"
      }
    },
    error: errorMessage
  };
}

/**
 * 格式化对话历史为API所需格式
 */
export function formatConversationHistory(messages: any[]): ChatMessage[] {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content,
    message_type: msg.type || 'message',
    timestamp: msg.timestamp,
    metadata: msg.metadata
  }));
}

/**
 * 提取当前规划内容
 */
export function extractCurrentPlan(messages: any[]): string {
  const planMessages = messages.filter(msg => 
    msg.role === 'assistant' && msg.type === 'plan'
  );
  
  if (planMessages.length > 0) {
    // 返回最新的规划
    return planMessages[planMessages.length - 1].content;
  }
  
  return "";
}

/**
 * 提取当前文档内容
 */
export function extractCurrentDocument(messages: any[]): string {
  const docMessages = messages.filter(msg => 
    msg.role === 'assistant' && msg.type === 'document'
  );
  
  if (docMessages.length > 0) {
    // 返回最新的文档
    return docMessages[docMessages.length - 1].content;
  }
  
  return "";
}
