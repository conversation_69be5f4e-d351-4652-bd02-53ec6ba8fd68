/* Chat页面专用样式 */

/* 防止页面滚动 */
.chat-page-container {
  height: 100vh;
  overflow: hidden;
}

/* 会话列表切换动画 */
.session-list-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 移动端会话列表滑入动画 */
.mobile-session-panel-enter {
  transform: translateX(-100%);
}

.mobile-session-panel-enter-active {
  transform: translateX(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-session-panel-exit {
  transform: translateX(0);
}

.mobile-session-panel-exit-active {
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 背景遮罩淡入淡出 */
.backdrop-fade-enter {
  opacity: 0;
}

.backdrop-fade-enter-active {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.backdrop-fade-exit {
  opacity: 1;
}

.backdrop-fade-exit-active {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* 会话列表按钮悬停效果 */
.session-toggle-btn {
  transition: all 0.2s ease-in-out;
}

.session-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 聊天区域布局过渡 */
.chat-layout-transition {
  transition: grid-template-columns 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式断点调整 */
@media (max-width: 1024px) {
  .session-list-desktop {
    display: none;
  }
}

@media (min-width: 1025px) {
  .session-list-mobile {
    display: none;
  }
}

/* 会话列表项悬停效果增强 */
.session-item {
  transition: all 0.2s ease-in-out;
}

.session-item:hover {
  transform: translateX(2px);
}

/* 滚动条样式优化 */
.session-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.session-list-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.session-list-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.session-list-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Markdown 内容样式 */
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Mermaid 图表容器样式 */
.mermaid-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow-x: auto;
}

.mermaid-container svg {
  max-width: 100%;
  height: auto;
}

/* Mermaid 错误显示样式 */
.mermaid-error {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: rgb(239, 68, 68);
}

.mermaid-error p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.mermaid-error pre {
  margin: 0;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 0.875rem;
  overflow-x: auto;
}

/* 暗色主题下的 Mermaid 样式 */
@media (prefers-color-scheme: dark) {
  .mermaid-container {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .mermaid-error {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
  }

  .mermaid-error pre {
    background: rgba(255, 255, 255, 0.1);
  }
}
