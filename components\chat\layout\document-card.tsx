"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, CheckCircle, Clock } from "lucide-react";

interface DocumentCardProps {
  showDocPanel: boolean;
  onOpenPanel: () => void;
  t: any;
}

export function DocumentCard({
  showDocPanel,
  onOpenPanel,
  t
}: DocumentCardProps) {


  return (
    <Card className="bg-gradient-to-br from-muted/40 to-muted/20 border-border/40 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
      <CardContent className="p-5 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 rounded-2xl flex items-center justify-center transition-all duration-300 bg-gradient-to-br from-green-100 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/20">
              <div className="relative">
                <FileText className="w-5 h-5 text-green-600 dark:text-green-400" />
                <CheckCircle className="w-3 h-3 text-green-500 absolute -top-1 -right-1 bg-background rounded-full" />
              </div>
            </div>
            <div className="space-y-1">
              <h3 className="font-semibold text-base leading-tight">
                {t('messages.document_completed')}
              </h3>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1.5 text-xs text-muted-foreground bg-background/60 px-2 py-1 rounded-full">
                  <Clock className="w-3 h-3" />
                  <span>{new Date().toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={onOpenPanel}
            className={`rounded-xl px-4 py-2 font-medium transition-all duration-200 ${
              showDocPanel
                ? "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50"
                : ""
            }`}
            variant={showDocPanel ? "secondary" : "default"}
          >
            {showDocPanel ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm">{t('buttons.opened')}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                <span className="text-sm">{t('buttons.open')}</span>
              </div>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
