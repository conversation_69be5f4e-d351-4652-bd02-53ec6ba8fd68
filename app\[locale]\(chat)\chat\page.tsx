import { getUserInfo } from "@/services/user";
import { redirect } from "next/navigation";
import { getLandingPage } from "@/services/page";
import ChatClient from "./chat-client";

export default async function ChatPage({ params }: { params: { locale: string } }) {
  // 检查用户认证状态
  const userInfo = await getUserInfo();
  if (!userInfo || !userInfo.email) {
    redirect(`/auth/signin?callbackUrl=${encodeURIComponent(`/${params.locale}/chat`)}`);
  }

  // 获取页面配置（包含header配置）
  const page = await getLandingPage(params.locale);

  // 如果认证通过，渲染聊天客户端组件
  return <ChatClient params={params} header={page.header} />;
}