{"extends": ["next/core-web-vitals"], "rules": {"react/no-unescaped-entities": "off", "@next/next/no-page-custom-font": "off", "prefer-const": "warn", "no-var": "error", "react/display-name": "off", "import/no-anonymous-default-export": "off", "@next/next/no-img-element": "warn", "jsx-a11y/alt-text": "warn", "@next/next/no-html-link-for-pages": "warn", "react-hooks/exhaustive-deps": "warn", "react-hooks/rules-of-hooks": "warn"}}