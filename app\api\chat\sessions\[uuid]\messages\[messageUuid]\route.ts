import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';
import { 
  findChatSessionByUuid, 
  findChatMessageByUuid,
  updateChatMessage,
  MessageRole,
  MessageType 
} from '@/models/chat';

// PUT /api/chat/sessions/[uuid]/messages/[messageUuid] - 更新消息
export async function PUT(
  req: NextRequest,
  { params }: { params: { uuid: string; messageUuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;
    const messageUuid = params.messageUuid;
    const body = await req.json();
    const { content, message_type, metadata } = body;

    // 验证会话是否属于当前用户
    const session = await findChatSessionByUuid(sessionUuid);
    console.log('Session found:', session ? 'yes' : 'no', 'sessionUuid:', sessionUuid);
    if (!session || session.user_uuid !== userInfo.uuid) {
      console.log('Session validation failed:', !session ? 'session not found' : 'user mismatch');
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // 验证消息是否存在且属于该会话
    const existingMessage = await findChatMessageByUuid(messageUuid);
    console.log('Message found:', existingMessage ? 'yes' : 'no', 'messageUuid:', messageUuid);
    if (!existingMessage || existingMessage.session_uuid !== sessionUuid) {
      console.log('Message validation failed:', !existingMessage ? 'message not found' : 'session mismatch');
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // 验证必需字段
    if (!content) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 });
    }

    // 更新消息
    const updatedMessage = await updateChatMessage(messageUuid, {
      content,
      message_type: message_type as MessageType || existingMessage.message_type,
      metadata: metadata ? JSON.stringify(metadata) : existingMessage.metadata,
    });

    if (!updatedMessage) {
      return NextResponse.json({ error: 'Failed to update message' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: updatedMessage,
    });
  } catch (error) {
    console.error('Failed to update chat message:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/chat/sessions/[uuid]/messages/[messageUuid] - 删除消息
export async function DELETE(
  req: NextRequest,
  { params }: { params: { uuid: string; messageUuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;
    const messageUuid = params.messageUuid;

    // 验证会话是否属于当前用户
    const session = await findChatSessionByUuid(sessionUuid);
    if (!session || session.user_uuid !== userInfo.uuid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // 验证消息是否存在且属于该会话
    const existingMessage = await findChatMessageByUuid(messageUuid);
    if (!existingMessage || existingMessage.session_uuid !== sessionUuid) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // 软删除消息（可以根据需要实现硬删除）
    const deletedMessage = await updateChatMessage(messageUuid, {
      content: '[已删除]',
      metadata: JSON.stringify({ deleted: true, deleted_at: new Date().toISOString() }),
    });

    return NextResponse.json({
      success: true,
      data: deletedMessage,
    });
  } catch (error) {
    console.error('Failed to delete chat message:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
