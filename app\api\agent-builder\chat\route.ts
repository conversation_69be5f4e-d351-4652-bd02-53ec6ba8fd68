import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';

const baseUrl = process.env.SHORT_PLANNING_BASEURL;
const chatUrl = `${baseUrl}/chat/conversation`;
const intentUrl = `${baseUrl}/chat/analyze-intent`;

export async function POST(req: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.api_key) {
      console.log("userInfo.api_key is empty")
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
    
    const body = await req.json();
    const { action, ...requestData } = body;
    
    // 根据action决定调用哪个端点
    const targetUrl = action === 'analyze-intent' ? intentUrl : chatUrl;
    
    console.log(`userInfo.api_key: ${userInfo.api_key}`)
    const response = await fetch(targetUrl, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userInfo.api_key}`,
      },
      body: JSON.stringify(requestData),
    });

    const data = await response.json();

    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
