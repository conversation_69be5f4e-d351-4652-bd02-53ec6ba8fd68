{"template": "gtplanner-template", "theme": "light", "header": {"brand": {"title": "GTPlanner", "logo": {"src": "/logo.svg", "alt": "GTPlanner"}, "url": "/"}, "nav": {"items": [{"title": "Features", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "Demo", "url": "/chat", "icon": "RiRobot2Line"}, {"title": "Use Cases", "url": "/#usage", "icon": "RiCodeLine"}]}, "buttons": [{"title": "Try Demo", "url": "/chat", "target": "_self", "variant": "link", "icon": "RiArrowRightLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Planner for Vibe Coding", "highlight_text": "Vibe Coding", "description": "AI-powered planning tool for modern developers.<br/>Design systems, generate docs, and enhance your development workflow.", "announcement": {"label": "NEW", "title": "🚀 AI-Powered Planning", "url": "/chat"}, "tip": "✨ Transform your development workflow", "buttons": [{"title": "Try Demo", "icon": "RiRobot2Line", "url": "/chat", "target": "_self", "variant": "default"}, {"title": "View Features", "icon": "RiSparklingFill", "url": "/#feature", "target": "_self", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"disabled": true, "title": "Built with modern development tools", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}]}, "introduce": {"name": "introduce", "title": "What is GTPlanner", "label": "Introduce", "description": "GTPlanner is an AI-powered planning tool designed for modern developers. It helps you design systems, generate documentation, and enhance your vibe coding workflow.", "items": [{"title": "AI-Powered Planning", "description": "Generate intelligent project plans and system architectures with advanced AI assistance.", "icon": "RiRobot2Line"}, {"title": "Smart Documentation", "description": "Automatically create comprehensive technical documentation from your planning sessions.", "icon": "RiFileTextLine"}, {"title": "Developer-Optimized", "description": "Built specifically for modern development workflows and vibe coding practices.", "icon": "RiCodeLine"}]}, "benefit": {"name": "benefit", "title": "Why <PERSON>ose <PERSON>lan<PERSON>", "label": "Benefits", "description": "Enhance your development workflow with AI-powered planning and documentation generation.", "items": [{"title": "Intelligent Planning", "description": "AI analyzes your requirements and generates structured project plans with clear implementation steps.", "icon": "RiBrainLine"}, {"title": "Vibe Coding Ready", "description": "Optimized for modern AI development tools like Cursor, Windsurf, and GitHub Copilot for seamless coding flow.", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Documentation Automation", "description": "Generate comprehensive technical documentation, API specs, and architecture diagrams automatically.", "icon": "RiFileTextLine"}]}, "usage": {"name": "usage", "title": "Developer Use Cases", "description": "See how GTPlanner enhances different development scenarios:", "text_align": "center", "items": [{"title": "System Architecture Design", "description": "Plan complex system architectures with AI assistance. Generate component diagrams and data flow charts."}, {"title": "API Development Planning", "description": "Design RESTful APIs and GraphQL schemas. Generate endpoint documentation and integration guides."}, {"title": "Feature Implementation", "description": "Break down complex features into manageable tasks. Create user stories and acceptance criteria."}, {"title": "Technical Documentation", "description": "Generate comprehensive docs, README files, and deployment guides for your projects."}]}, "feature": {"name": "feature", "title": "Core Features", "description": "Everything you need for intelligent development planning and documentation.", "items": [{"title": "AI Planning Assistant", "description": "Advanced AI analyzes requirements and generates structured project plans with implementation steps.", "icon": "RiRobot2Line"}, {"title": "Interactive Chat Interface", "description": "Natural conversation flow for refining plans and generating documentation iteratively.", "icon": "RiChatSmile3Line"}, {"title": "Document Generation", "description": "Automatically create technical docs, API specifications, and architecture diagrams.", "icon": "RiFileTextLine"}, {"title": "Vibe Coding Integration", "description": "Optimized outputs for modern AI development tools like Cursor and GitHub Copilot.", "icon": "RiCodeLine"}, {"title": "Multi-language Support", "description": "Full internationalization support with seamless language switching.", "icon": "RiTranslate2"}, {"title": "Export & Share", "description": "Export plans and documentation in multiple formats for team collaboration.", "icon": "RiShareLine"}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions", "description": "Learn more about GTPlanner and how it can enhance your development workflow.", "items": [{"title": "What is <PERSON><PERSON><PERSON><PERSON> and how does it work?", "description": "GTPlanner is an AI-powered planning tool designed for modern developers. It analyzes your requirements and generates structured project plans, system architectures, and comprehensive documentation to enhance your vibe coding workflow."}, {"title": "How does GTPlanner support vibe coding?", "description": "GTPlanner generates outputs optimized for modern AI development tools like Cursor, Windsurf, and GitHub Copilot. The structured plans and documentation help you maintain coding flow and productivity."}, {"title": "What types of projects can I plan with GTPlanner?", "description": "GTPlanner supports various development projects including web applications, APIs, system architectures, microservices, and more. It's particularly effective for planning complex software systems and technical implementations."}, {"title": "Can I export and share the generated plans?", "description": "Yes! GTPlanner allows you to export plans and documentation in multiple formats including Markdown, which can be easily shared with your team or integrated into your project repositories."}, {"title": "Is GTPlanner suitable for team collaboration?", "description": "Absolutely! The generated documentation and plans are designed to be clear and comprehensive, making them perfect for team collaboration and knowledge sharing across development teams."}, {"title": "Does GTPlanner support multiple programming languages?", "description": "Yes, GTPlanner can generate plans and documentation for projects using various programming languages and frameworks. The AI understands different technology stacks and provides relevant recommendations."}]}, "cta": {"name": "cta", "title": "Start Planning with AI", "description": "Transform your development workflow with intelligent planning.", "buttons": [{"title": "Try Demo", "url": "/chat", "target": "_self", "icon": "RiRobot2Line"}, {"title": "View Features", "url": "/#feature", "target": "_self", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "GTPlanner", "description": "AI-powered planning tool for modern developers. Enhance your vibe coding workflow with intelligent project planning and documentation generation.", "logo": {"src": "/logo.svg", "alt": "GTPlanner"}, "url": "/"}, "copyright": "© 2025 • GTPlanner All rights reserved.", "nav": {"items": [{"title": "Product", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Demo", "url": "/chat", "target": "_self"}, {"title": "Use Cases", "url": "/#usage", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documentation", "url": "/#faq", "target": "_self"}, {"title": "GitHub", "url": "#", "target": "_blank"}]}]}, "social": {"items": [{"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "#", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "#", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}