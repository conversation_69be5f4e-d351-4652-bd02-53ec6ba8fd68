/**
 * Server-side URL configuration for Next.js 14 App Router
 * This file is specifically for server components and API routes
 * where all environment variables are available
 */

export const serverUrls = {
  // Social Media URLs
  social: {
    github: process.env.NEXT_PUBLIC_GITHUB_URL || "https://github.com/gtplanner",
    githubTemplate: process.env.NEXT_PUBLIC_GITHUB_TEMPLATE_URL || "https://github.com/TABai/TAB-template-one",
    discord: process.env.NEXT_PUBLIC_DISCORD_URL || "https://discord.gg/HQNnrzjZQS",
    twitter: process.env.NEXT_PUBLIC_TWITTER_URL || "https://x.com/TABai",
  },

  // Contact Information
  contact: {
    supportEmail: process.env.NEXT_PUBLIC_SUPPORT_EMAIL || "<EMAIL>",
    get supportEmailUrl() {
      return `mailto:${this.supportEmail}`;
    },
  },

  // Documentation URLs
  docs: {
    shadcnThemes: process.env.NEXT_PUBLIC_SHADCN_THEMES_URL || "https://ui.shadcn.com/themes",
    githubCli: process.env.NEXT_PUBLIC_GITHUB_CLI_URL || "https://cli.github.com/",
    tabAi: process.env.NEXT_PUBLIC_TAB_AI_DOCS_URL || "https://docs.TAB.ai",
  },

  // Site URLs
  site: {
    url: process.env.NEXT_PUBLIC_SITE_URL || "https://vibecoding.sop.best",
    homepage: process.env.NEXT_PUBLIC_HOMEPAGE_URL || "https://vibecoding.sop.best",
    webUrl: process.env.NEXT_PUBLIC_WEB_URL || "http://localhost:3000",
    tabAi: process.env.NEXT_PUBLIC_TAB_AI_URL || "https://TAB.ai",
  },

  // Payment URLs
  payment: {
    successUrl: process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "http://localhost:3000/my-orders",
    failUrl: process.env.NEXT_PUBLIC_PAY_FAIL_URL || "http://localhost:3000/#pricing",
    cancelUrl: process.env.NEXT_PUBLIC_PAY_CANCEL_URL || "http://localhost:3000/#pricing",
  },
} as const;

/**
 * Helper function to get admin social links for server components
 */
export const getServerAdminSocialLinks = () => [
  {
    title: "Home",
    url: "/",
    target: "_blank",
    icon: "RiHomeLine",
  },
  {
    title: "Github",
    url: serverUrls.social.githubTemplate,
    target: "_blank",
    icon: "RiGithubLine",
  },
  {
    title: "Discord",
    url: serverUrls.social.discord,
    target: "_blank",
    icon: "RiDiscordLine",
  },
  {
    title: "X",
    url: serverUrls.social.twitter,
    target: "_blank",
    icon: "RiTwitterLine",
  },
];

/**
 * Helper function to get social media URLs for server components
 */
export const getServerSocialLinks = () => [
  {
    title: "Github",
    icon: "RiGithubFill",
    url: serverUrls.social.github,
    target: "_blank",
  },
  {
    title: "Email",
    icon: "RiMailLine", 
    url: serverUrls.contact.supportEmailUrl,
    target: "_self",
  },
];

export default serverUrls;
