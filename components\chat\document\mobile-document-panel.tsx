"use client";
import { DocumentPanelHeader } from "./document-panel-header";
import { DocumentPanelContent } from "./document-panel-content";
import { AnalysisPanel } from "../ui/analysis-panel";
import { ChatState } from "@/types/conversation";

interface MobileDocumentPanelProps {
  show: boolean;
  content: string;
  onChange: (content: string) => void;
  isMarkdownPreview: boolean;
  onTogglePreview: (preview: boolean) => void;
  onClose: () => void;
  isViewingHistory: boolean;
  onBackToProgress: () => void;
  state: ChatState;
  streamingAnalysis?: string; // 流式分析内容
  analysisContent?: string; // 完整的分析内容
  t: any;
}

export function MobileDocumentPanel({
  show,
  content,
  onChange,
  isMarkdownPreview,
  onTogglePreview,
  onClose,
  isViewingHistory,
  onBackToProgress,
  state,
  streamingAnalysis,
  analysisContent,
  t
}: MobileDocumentPanelProps) {
  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background flex flex-col">
      <DocumentPanelHeader
        t={t}
        isMarkdownPreview={isMarkdownPreview}
        onTogglePreview={onTogglePreview}
        onClose={onClose}
        hasContent={!!content.trim()}
        isLoading={state === ChatState.LONG_LOADING}
        isMobile={true}
        content={content}
      />
      <div className="flex-1 flex flex-col overflow-hidden min-h-0 p-4 space-y-4">
        {/* 思考过程面板 */}
        {(streamingAnalysis || analysisContent) && (
          <div className="flex-shrink-0">
            <AnalysisPanel
              content={streamingAnalysis || analysisContent || ''}
              isStreaming={!!streamingAnalysis}
              defaultExpanded={!!streamingAnalysis}
            />
          </div>
        )}

        {/* 文档内容 */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full bg-background/60 rounded-2xl border border-border/30 shadow-sm overflow-hidden">
            <DocumentPanelContent
              content={content}
              onChange={onChange}
              isMarkdownPreview={isMarkdownPreview}
              isViewingHistory={isViewingHistory}
              onBackToProgress={onBackToProgress}
              state={state}
              t={t}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
