import Blog from "@/components/blocks/blog";
import { Blog as BlogType, BlogItem } from "@/types/blocks/blog";
import { getPostsByLocale } from "@/models/post";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations();

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/posts`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/posts`;
  }

  return {
    title: t("blog.title"),
    description: t("blog.description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function ({ params }: { params: { locale: string } }) {
  const t = await getTranslations();

  const posts = await getPostsByLocale(params.locale);

  // Convert database types to BlogItem types
  const blogItems: BlogItem[] | undefined = posts?.map(post => ({
    slug: post.slug || undefined,
    title: post.title || undefined,
    description: post.description || undefined,
    author_name: post.author_name || undefined,
    author_avatar_url: post.author_avatar_url || undefined,
    created_at: post.created_at?.toISOString() || undefined,
    locale: post.locale || undefined,
    cover_url: post.cover_url || undefined,
    content: post.content || undefined,
  }));

  const blog: BlogType = {
    title: t("blog.title"),
    description: t("blog.description"),
    items: blogItems,
    read_more_text: t("blog.read_more_text"),
  };

  return <Blog blog={blog} />;
}
