/**
 * Dynamic landing page data generator
 * This replaces static JSON files with dynamic configuration using environment variables
 */

import { getFooterConfig, getNavConfig, getHeaderButtonsConfig, getHeroButtonsConfig } from "./landing";

/**
 * Generate complete landing page configuration
 */
export const getLandingPageData = (locale: string = "en") => {
  const isZh = locale === "zh";
  
  return {
    template: "gtplanner-template",
    theme: "light",
    header: {
      brand: {
        title: "GTPlanner",
        logo: {
          src: "/logo.png",
          alt: "GTPlanner"
        },
        url: isZh ? "/zh" : "/"
      },
      nav: getNavConfig(locale),
      buttons: getHeaderButtonsConfig(locale),
      show_sign: true,
      show_theme: true,
      show_locale: true
    },
    hero: {
      title: isZh ? "Vibe Coding 专用规划工具" : "Planner for Vibe Coding",
      highlight_text: "Vibe Coding",
      description: isZh 
        ? "面向现代开发者的AI驱动规划工具。<br/>设计系统、生成文档，提升你的开发工作流程。"
        : "AI-powered planning tool for modern developers.<br/>Design systems, generate docs, and enhance your development workflow.",
      announcement: {
        label: isZh ? "新功能" : "NEW",
        title: isZh ? "🚀 AI驱动的智能规划" : "🚀 AI-Powered Planning",
        url: "/chat"
      },
      tip: isZh ? "✨ 革新你的开发工作流程" : "✨ Transform your development workflow",
      buttons: getHeroButtonsConfig(locale),
      show_happy_users: false,
      show_badge: false
    },
    footer: getFooterConfig(locale)
  };
};

/**
 * Get footer data specifically (for components that only need footer)
 */
export const getFooterData = (locale: string = "en") => {
  return getFooterConfig(locale);
};
