"use client";
import { useState, useEffect } from "react";

interface DynamicProgressBarProps {
  startTime: number;
}

export function DynamicProgressBar({ startTime }: DynamicProgressBarProps) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const totalDuration = 14000; // 14秒总时长

    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / totalDuration) * 100, 100);
      setProgress(newProgress);

      if (newProgress < 100) {
        // 随机间隔0.5-1.5秒
        const nextInterval = 500 + Math.random() * 1000;
        setTimeout(updateProgress, nextInterval);
      }
    };

    // 初始延迟后开始更新
    const initialDelay = 500 + Math.random() * 1000;
    setTimeout(updateProgress, initialDelay);
  }, [startTime]);

  return (
    <div className="w-64 h-2 bg-muted rounded-full overflow-hidden">
      <div
        className="h-full bg-primary rounded-full transition-all duration-1000 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}

interface LongDocProgressBarProps {
  startTime: number;
}

export function LongDocProgressBar({ startTime }: LongDocProgressBarProps) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const totalDuration = 120000; // 2分钟总时长
    const updateInterval = 4000; // 4秒推进一次

    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / totalDuration) * 100, 100);
      setProgress(newProgress);

      if (newProgress < 100) {
        setTimeout(updateProgress, updateInterval);
      }
    };

    // 初始延迟后开始更新
    setTimeout(updateProgress, updateInterval);
  }, [startTime]);

  return (
    <div className="w-full h-2 bg-muted rounded-full overflow-hidden mb-4">
      <div
        className="h-full bg-primary rounded-full transition-all duration-1000 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
