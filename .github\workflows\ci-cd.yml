name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
    tags:
      - 'v*'
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      skip_tests:
        description: 'Skip tests and linting'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository_owner }}/gtplanner-frontend

jobs:
  # Determine environment based on trigger
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      should_deploy: ${{ steps.env.outputs.should_deploy }}
    steps:
      - name: Determine environment
        id: env
        run: |
          echo "Event name: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Skip tests: ${{ inputs.skip_tests }}"

          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # Manual trigger - always development environment
            echo "Manual trigger detected - setting development environment"
            echo "environment=development" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            # Tag push - production (only way to deploy to production)
            echo "Tag push detected - setting production environment"
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" && "${{ github.event_name }}" == "push" ]]; then
            # Push to main - development
            echo "Main branch push detected - setting development environment"
            echo "environment=development" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          else
            # PR or other - no deployment
            echo "PR or other event - no deployment"
            echo "environment=development" >> $GITHUB_OUTPUT
            echo "should_deploy=false" >> $GITHUB_OUTPUT
          fi

          echo "Final environment: $(cat $GITHUB_OUTPUT | grep environment | cut -d'=' -f2)"
          echo "Final should_deploy: $(cat $GITHUB_OUTPUT | grep should_deploy | cut -d'=' -f2)"

  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    if: ${{ !inputs.skip_tests }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint --max-warnings 50

      - name: Run type checking
        run: pnpm build

  build-and-push:
    needs: [test, determine-environment]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    if: needs.determine-environment.outputs.should_deploy == 'true' && (always() && (needs.test.result == 'success' || needs.test.result == 'skipped'))
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      environment: ${{ needs.determine-environment.outputs.environment }}
    steps:
      - name: Debug build-and-push
        run: |
          echo "Should deploy: ${{ needs.determine-environment.outputs.should_deploy }}"
          echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
          echo "Test result: ${{ needs.test.result }}"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Prepare image name
        id: image
        run: |
          # Convert repository owner to lowercase for Docker compliance
          REPO_OWNER_LOWER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
          IMAGE_NAME="${REPO_OWNER_LOWER}/gtplanner-frontend"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT
          echo "full=${REGISTRY}/${IMAGE_NAME}" >> $GITHUB_OUTPUT

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GHCR_TOKEN || secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ steps.image.outputs.full }}
          tags: |
            type=ref,event=branch,suffix=-{{sha}}-${{ needs.determine-environment.outputs.environment }}
            type=ref,event=pr,suffix=-{{sha}}-${{ needs.determine-environment.outputs.environment }}
            type=ref,event=tag,suffix=-${{ needs.determine-environment.outputs.environment }}
            type=raw,value=latest-${{ needs.determine-environment.outputs.environment }},enable={{is_default_branch}}
          flavor: |
            latest=false
          labels: |
            org.opencontainers.image.title=GTPlanner Frontend (${{ needs.determine-environment.outputs.environment }})
            org.opencontainers.image.description=GTPlanner Frontend Application - ${{ needs.determine-environment.outputs.environment }} environment

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_ENV=${{ needs.determine-environment.outputs.environment }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: needs.build-and-push.outputs.environment != ''
    environment: ${{ needs.build-and-push.outputs.environment }}
    steps:
      - name: Debug deploy
        run: |
          echo "Build-and-push environment: ${{ needs.build-and-push.outputs.environment }}"
          echo "Deploy condition check: ${{ needs.build-and-push.outputs.environment != '' }}"

      - name: Set deployment variables
        id: deploy-vars
        run: |
          ENV="${{ needs.build-and-push.outputs.environment }}"
          if [ "$ENV" = "production" ]; then
            echo "deployment_name=${{ secrets.DEPLOYMENT_NAME }}" >> $GITHUB_OUTPUT
            echo "namespace=${{ secrets.NAMESPACE }}" >> $GITHUB_OUTPUT
          else
            echo "deployment_name=${{ secrets.DEPLOYMENT_DEVELOPMENT_NAME }}" >> $GITHUB_OUTPUT
            echo "namespace=${{ secrets.NAMESPACE }}" >> $GITHUB_OUTPUT
          fi

      - name: Deploy to Kubernetes
        env:
          K8S_TOKEN: ${{ secrets.K8S_TOKEN }}
          VERSION: ${{ github.sha }}
          K8S_API_URL: ${{ secrets.K8S_API_URL }}
          DEPLOYMENT_NAME: ${{ steps.deploy-vars.outputs.deployment_name }}
          NAMESPACE: ${{ steps.deploy-vars.outputs.namespace }}
          ENVIRONMENT: ${{ needs.build-and-push.outputs.environment }}
        run: |
          # Prepare lowercase repository name for Docker compliance
          REPO_OWNER_LOWER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
          CI_REGISTRY_IMAGE="${{ env.REGISTRY }}/${REPO_OWNER_LOWER}/gtplanner-frontend"

          # Build image tag based on trigger type
          if [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            # Tag-based deployment
            TAG_NAME=${GITHUB_REF#refs/tags/}
            IMAGE_TAG="${CI_REGISTRY_IMAGE}:${TAG_NAME}-${ENVIRONMENT}"
          else
            # Branch-based deployment
            BRANCH_NAME=${GITHUB_REF#refs/heads/}
            # Sanitize branch name for Docker tag compliance
            BRANCH_NAME=${BRANCH_NAME//\//-}    # Replace / with -
            BRANCH_NAME=${BRANCH_NAME//_/-}     # Replace _ with -
            BRANCH_NAME=${BRANCH_NAME,,}        # Convert to lowercase
            BRANCH_NAME=${BRANCH_NAME#-}        # Remove leading -
            BRANCH_NAME=${BRANCH_NAME%-}        # Remove trailing -
            IMAGE_TAG="${CI_REGISTRY_IMAGE}:${BRANCH_NAME}-${VERSION:0:7}-${ENVIRONMENT}"
          fi

          echo "Deploying to $ENVIRONMENT environment"
          echo "Deployment name: $DEPLOYMENT_NAME"
          echo "Image: $IMAGE_TAG"

          # Update the deployment using the provided API
          curl -X PATCH \
            -H "content-type: application/strategic-merge-patch+json" \
            -H "Authorization: Bearer $K8S_TOKEN" \
            -d "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"gtplanner-fronted\",\"image\":\"$IMAGE_TAG\"}],\"initContainers\":[{\"name\":\"db-migration\", \"image\":\"$IMAGE_TAG\",\"args\":[\"migrate\"]}]}}}}" \
            "$K8S_API_URL/apis/apps/v1/namespaces/$NAMESPACE/deployments/$DEPLOYMENT_NAME"

          echo "Deployment updated successfully"

      - name: Verify deployment
        env:
          K8S_TOKEN: ${{ secrets.K8S_TOKEN }}
          K8S_API_URL: ${{ secrets.K8S_API_URL }}
          DEPLOYMENT_NAME: ${{ steps.deploy-vars.outputs.deployment_name }}
          NAMESPACE: ${{ steps.deploy-vars.outputs.namespace }}
        run: |
          # Wait for rollout to complete
          echo "Waiting for deployment to complete..."

          # Check deployment status
          for i in {1..30}; do
            STATUS=$(curl -s -H "Authorization: Bearer $K8S_TOKEN" \
              "$K8S_API_URL/apis/apps/v1/namespaces/$NAMESPACE/deployments/$DEPLOYMENT_NAME" | \
              jq -r '.status.conditions[] | select(.type=="Progressing") | .status')

            if [ "$STATUS" = "True" ]; then
              echo "Deployment completed successfully"
              break
            fi

            echo "Waiting for deployment... ($i/30)"
            sleep 10
          done
