import { urls } from '@/lib/config/urls';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy | GTPlanner',
  description: 'Privacy policy for GTPlanner - AI-powered planning tool for modern developers.',
};

export default function PrivacyPolicy() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8 prose prose-gray dark:prose-invert">
      <h1>Privacy Policy for GTPlanner</h1>

      <h2>Introduction</h2>
      <p>
        Welcome to GTPlanner, the <strong>AI-powered planning tool</strong> designed to help modern developers plan and document their projects efficiently. At GTPlanner, your privacy is of utmost importance to us, and this privacy policy outlines how we collect, use, and protect your personal information when you use our services.
      </p>

      <h2>Information Collection and Use</h2>
      <p>We collect the following types of data to provide you with a better experience while using GTPlanner:</p>

      <ol>
        <li>
          <strong>Account Information</strong>
          <ul>
            <li><strong>What We Collect</strong>: This includes your name, email address, and any other information you provide when creating an account.</li>
            <li><strong>Purpose</strong>: To manage your account and provide customer support.</li>
          </ul>
        </li>

        <li>
          <strong>Usage Details</strong>
          <ul>
            <li><strong>What We Collect</strong>: Information about how you use GTPlanner, including your interactions, features accessed, and usage frequency.</li>
            <li><strong>Purpose</strong>: To analyze user engagement and improve our services.</li>
          </ul>
        </li>

        <li>
          <strong>Device Information</strong>
          <ul>
            <li><strong>What We Collect</strong>: Data regarding the devices you use to access GTPlanner, such as device type, operating system, and browser type.</li>
            <li><strong>Purpose</strong>: To optimize our services for different devices and ensure compatibility.</li>
          </ul>
        </li>

        <li>
          <strong>Cookies</strong>
          <ul>
            <li><strong>What We Collect</strong>: Small data files placed on your device that help us track user preferences and improve user experience.</li>
            <li><strong>Purpose</strong>: To enhance the functionality of our services and personalize your experience.</li>
          </ul>
        </li>
      </ol>

      <h2>Data Sharing and Disclosure</h2>
      <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except in the following circumstances:</p>
      <ul>
        <li><strong>Service Providers</strong>: We may share your information with trusted third-party service providers who assist us in operating our website and conducting our business.</li>
        <li><strong>Legal Requirements</strong>: We may disclose your information when required by law or to protect our rights, property, or safety.</li>
      </ul>

      <h2>Data Security</h2>
      <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure.</p>

      <h2>Your Rights</h2>
      <p>You have the right to:</p>
      <ul>
        <li>Access and update your personal information</li>
        <li>Request deletion of your personal data</li>
        <li>Opt-out of certain communications</li>
        <li>Request a copy of your data</li>
      </ul>

      <h2>Changes to This Policy</h2>
      <p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "Last Updated" date.</p>

      <h2>Contact Us</h2>
      <p>If you have any questions or concerns about this privacy policy or our data practices, please contact us at:</p>
      
      <p>
        <strong>Website</strong>: <a href={urls.site.url} target="_blank" rel="noopener noreferrer">{urls.site.url}</a><br/>
        <strong>Email</strong>: <a href={urls.contact.supportEmailUrl}>{urls.contact.supportEmail}</a>
      </p>

      <p>By using GTPlanner, you consent to our privacy policy and agree to its terms. Thank you for trusting us with your information!</p>
    </div>
  );
}
