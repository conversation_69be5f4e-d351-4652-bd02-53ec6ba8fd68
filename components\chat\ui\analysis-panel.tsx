'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Brain, Sparkles, Maximize2, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { MarkdownRenderer } from './markdown-renderer';

interface AnalysisPanelProps {
  content: string;
  isStreaming?: boolean;
  defaultExpanded?: boolean;
}

export function AnalysisPanel({
  content,
  isStreaming = false,
  defaultExpanded = false
}: AnalysisPanelProps) {
  const t = useTranslations('chat');
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [wasStreaming, setWasStreaming] = useState(false);

  // 监听流式状态变化，完成后自动收起
  useEffect(() => {
    if (isStreaming) {
      setIsExpanded(true); // 开始流式输出时展开
      setWasStreaming(true);
    } else if (wasStreaming && !isStreaming && content) {
      // 从流式状态变为非流式状态，且有内容时，延迟收起
      const timer = setTimeout(() => {
        setIsExpanded(false);
        setWasStreaming(false); // 重置状态
      }, 1500); // 1.5秒后自动收起，给用户足够时间看到"思考结束"

      return () => clearTimeout(timer);
    }
  }, [isStreaming, content, wasStreaming]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      <div className="bg-background/60 rounded-2xl border border-border/30 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* 标题栏 */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/20 transition-all duration-200 group"
        onClick={toggleExpanded}
      >
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-xl transition-all duration-300 ${
            isStreaming
              ? "bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 animate-pulse"
              : "bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/20"
          }`}>
            {isStreaming ? (
              <div className="relative">
                <Brain className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <Sparkles className="h-3 w-3 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
              </div>
            ) : (
              <Brain className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            )}
          </div>
          <div>
            <span className="font-semibold text-foreground group-hover:text-primary transition-colors">
              {t('messages.analysis_title')}
            </span>
            {isStreaming ? (
              <div className="flex items-center gap-2 mt-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  {t('loading.analyzing')}
                </span>
              </div>
            ) : content && (
              <div className="mt-1">
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  {t('ui.thinking_completed')}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {content && (
            <div className="bg-background/60 px-3 py-1 rounded-full">
              <span className="text-xs text-muted-foreground font-medium">
                {content.length} {t('ui.characters')}
              </span>
            </div>
          )}
          {content && !isStreaming && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsFullscreen(true);
              }}
              className="p-1 rounded-lg hover:bg-background/60 transition-colors"
              title={t('mermaid.click_to_fullscreen')}
            >
              <Maximize2 className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
            </button>
          )}
          <div className="p-1 rounded-lg group-hover:bg-background/60 transition-colors">
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
            ) : (
              <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
            )}
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <div className="border-t border-border/30">
          {content ? (
            <div className="p-5 max-h-[400px] overflow-y-auto">
              <div className="bg-muted/30 rounded-xl p-4 border border-border/20">
                <MarkdownRenderer
                  content={content}
                  skipMermaid={isStreaming}
                />
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Brain className="w-8 h-8 text-muted-foreground/50 mx-auto mb-3" />
              <div className="text-muted-foreground text-sm italic">
                {t('messages.no_analysis_content')}
              </div>
            </div>
          )}
        </div>
      )}
      </div>

      {/* 全屏模态框 */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4">
          <div className="bg-background rounded-2xl border border-border/30 shadow-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col">
            {/* 全屏标题栏 */}
            <div className="flex items-center justify-between p-6 border-b border-border/30">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/20">
                  <Brain className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <span className="font-semibold text-foreground text-lg">
                    {t('messages.analysis_title')}
                  </span>
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                    {t('ui.thinking_completed')}
                  </div>
                </div>
              </div>
              <button
                onClick={() => setIsFullscreen(false)}
                className="p-2 rounded-lg hover:bg-muted/20 transition-colors"
                title="关闭全屏"
              >
                <X className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
              </button>
            </div>

            {/* 全屏内容 */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="bg-muted/30 rounded-xl p-6 border border-border/20 h-full">
                <MarkdownRenderer
                  content={content}
                  skipMermaid={false} // 全屏时总是渲染mermaid
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default AnalysisPanel;
