{"metadata": {"title": "Planner for Vibe Coding | GTPlanner", "description": "GTPlanner is an AI-powered planning tool for modern developers. Design systems, generate docs, and enhance your development workflow.", "keywords": "<PERSON><PERSON><PERSON>ner, AI Planning Tool, Development Planning, Vibe Coding"}, "common": {"back": "Back", "website_url": "vibecoding.sop.best", "support_email": "<EMAIL>"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System", "avatar": "User Avatar", "guest": "Guest"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "casdoor_sign_in": "Sign in with GTPlanner", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with GTPlanner.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about GTPlanner", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy GTPlanner, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought GTPlanner", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "Share": {"not_public": {"title": "This conversation is not publicly shared", "description": "The owner of this session has not set this conversation to be publicly shared. If you are the session owner, please select the \"Share\" option in the session list to make this session public.", "go_to_app": "Go to App"}, "not_found": {"title": "Shared conversation not found", "subtitle": "Unable to find this session", "description": "The share link you accessed may have expired, or the session may have been deleted. Please check if the link is correct, or contact the sharer for the latest link.", "go_to_app": "Go to App"}, "page": {"shared_by": "Shared by {user}", "default_user": "User", "public_badge": "Public Share", "copy_link": "Copy Link", "go_to_app": "Go to App", "copy_success": "Share link copied to clipboard", "copy_failed": "Copy failed, please copy manually", "footer_message": "This is a publicly shared conversation. You cannot interact on this page.", "footer_link": "Go to app to start your conversation"}, "metadata": {"not_found_title": "Shared conversation not found", "not_public_title": "{title} - Private conversation", "not_public_description": "This conversation is not publicly shared", "shared_title": "{title} - Shared conversation", "shared_description": "View conversation shared by {user}"}}, "chat": {"welcome": {"title": "Welcome to AI Assistant", "subtitle": "Intelligent Planning & Content Generation", "click_to_start": "Click to start →", "instruction": "Choose an example to get started, or describe your requirements in the input box below"}, "examples": {"youtube": {"title": "📺 YouTube Video Summarizer", "description": "Intelligently analyze video content and generate structured summaries", "prompt": "I need a tool that can automatically fetch video subtitles when I provide a YouTube link, analyze the content, and generate a structured summary that includes key points, core arguments, and corresponding timestamps."}, "document": {"title": "📄 Document Analysis Assistant", "description": "Deep analysis of documents to extract key information", "prompt": "Please help me design a document analysis tool. Users should be able to upload legal contracts in PDF or Word format, and the system needs to automatically identify and extract key details like Party A, Party B, contract value, effective date, and breach clauses, while also highlighting potentially risky terms."}, "report": {"title": "📊 Data Report Generator", "description": "Transform data into professional reports", "prompt": "I want to create a data report generator. After a user uploads an Excel file with monthly sales and marketing expenses, the system should automatically generate a monthly business report with data charts (trend lines, pie charts), KPI analysis, and business growth suggestions."}, "research": {"title": "🔍 Research Assistant", "description": "Systematic research and analysis tool", "prompt": "I need a research assistant to analyze 'The Application of AI in Medical Diagnostics.' It should search and filter relevant academic papers and reports from the last five years and compile a comprehensive report covering the current state of technology, major challenges, and future trends."}, "content": {"title": "✍️ Content Creation Tool", "description": "AI-driven content creation assistant", "prompt": "Design a content creation tool for social media managers. A user inputs a topic (e.g., 'healthy eating') and a target platform (e.g., 'Instagram'), and the tool will generate five draft posts complete with emojis and relevant hashtags."}, "strategy": {"title": "💡 Strategy Planning Assistant", "description": "Develop systematic strategic solutions", "prompt": "I want to build a Strategy Planning Assistant tool. It should guide a user by asking for key business inputs like their target market, core products, and competitors. The tool should then provide a framework to help them complete a SWOT analysis and finally generate a draft strategy document outlining market positioning, suggested marketing initiatives, and budget allocation."}}, "timer": {"elapsed_time": "Elapsed: {time}"}, "loading": {"analyzing_requirements": "Analyzing your requirements...", "processing_message": "Processing your message", "generating_document": "Generating design document...", "estimated_time": "Estimated completion time: about 2 minutes", "replying": "Replying...", "analyzing": "Analyzing...", "generating": "Generating"}, "placeholders": {"welcome_input": "💬 Or enter your requirements...", "analyzing": "Analyzing, please wait...", "processing": "Processing your message...", "tell_me_changes": "Tell me what you want to change...", "generating_document": "Generating design document, please wait...", "continue_chat": "Continue conversation...", "edit_plan": "Edit your plan...", "document_content": "Design document content..."}, "buttons": {"save": "Save", "edit_manually": "Edit Manually", "generate_document": "Generate Complete Document", "regenerate_document": "Regenerate Document", "view_version": "View This Version", "back_to_progress": "Back to Progress", "open": "Open", "opened": "Opened"}, "messages": {"plan_title": "Here's my plan to tackle that topic. Let me know if you need to make changes.", "design_completed": "Design Completed!", "document_version": "Design Document Version", "viewing_history": "Viewing Historical Version", "design_document": "Design Document", "document_generating": "Design Document Generating", "document_completed": "Design Document Completed", "waiting_for_plan": "Waiting for plan generation", "continue_conversation": "Continue the conversation to generate a planning solution", "analysis_title": "Requirements Analysis Process", "no_analysis_content": "No analysis content available"}, "tooltips": {"download_file": "Download design.md file", "update_description": "We will use the requirements description here to update the generated document", "prompt": "Prompt"}, "canvas": {"title": "Canvas Collaborative Editing", "mode_active": "Canvas Mode", "toggle_mode": "Canvas Mode", "selected_text": "Selected Text", "instruction_placeholder": "Tell me how to modify this text...", "instruction_hint": "Press Ctrl+Enter to send instruction", "document_placeholder": "Edit your document here...", "processing": "Processing modification request...", "modification_suggestion": "Modification Suggestion", "lines": "Lines {start}-{end}", "pending_modifications": "{count} pending modifications", "undo": "Undo", "redo": "Redo", "conversation_title": "Canvas Conversation", "exit_mode": "Exit Canvas", "no_conversation": "No conversation yet", "start_conversation_hint": "Start chatting with AI to collaboratively edit documents", "conversation_placeholder": "Chat with AI to collaboratively edit documents...", "initializing": "Initializing Canvas document", "please_wait": "Please wait..."}, "mermaid": {"title": "Mermaid Chart", "instructions": "Scroll to zoom • Drag to move • Click outside or press ESC to close", "instructions_mobile": "Single finger to drag • Two fingers to zoom • Tap outside to close", "zoom_in": "Zoom In", "zoom_out": "Zoom Out", "reset_view": "Reset View", "close_fullscreen": "Close Fullscreen", "click_to_fullscreen": "Click to view fullscreen", "rendering_error": "Chart syntax error, unable to render", "auto_fix_attempted": "Auto-fix attempted", "view_original_code": "View original code", "view_fixed_code": "View fixed code"}, "ui": {"thinking_completed": "Thinking Completed", "characters": "characters", "edit": "Edit", "preview": "Preview", "icon": "Icon", "copy": "Copy", "copy_success": "Copied!", "copy_document": "Copy document content", "download": "Download", "close": "Close"}, "session_list": {"title": "Session List", "search_placeholder": "Search sessions...", "loading": "Loading...", "no_sessions": "No sessions", "no_search_results": "No matching sessions found", "new_chat_tooltip": "Prepare new chat", "new_chat_preparing": "Already in new chat preparation state", "hide_list_tooltip": "Hide session list (Ctrl+B)", "rename": "<PERSON><PERSON>", "share": "Share", "unshare": "Unshare", "copy_link": "Copy Link", "share_success": "Session is now publicly shared", "unshare_success": "Session sharing has been disabled", "copy_success": "Share link copied to clipboard", "delete": "Delete", "delete_confirm_title": "Confirm Delete Session", "delete_confirm_description": "This action will permanently delete this session and all its message records. This cannot be undone. Are you sure you want to continue?", "cancel": "Cancel", "time_groups": {"today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "this_month": "This Month", "earlier": "Earlier"}}, "errors": {"plan_failed": "Plan generation failed, please try again.", "document_failed": "Document generation failed, please try again.", "network_error": "Network error, please check your connection and try again."}}, "legal": {"terms_of_service": {"title": "Terms of Service", "description": "Terms of Service for GTPlanner - AI-powered planning tool for developers", "introduction": {"title": "Introduction and Acceptance of Terms", "content": "Welcome to GTPlanner, an AI-powered planning tool designed for modern developers to plan and document their projects efficiently. By accessing or using our service, you agree to be bound by these Terms of Service. If you do not agree with any of these terms, please do not use our service."}, "use_of_service": {"title": "Use of the Service", "content": "GTPlanner provides users with an AI-powered platform to plan development projects, generate documentation, and enhance their development workflow. You agree to use the service in accordance with all applicable local, state, national, and international laws and regulations."}, "user_accounts": {"title": "User Accounts and Registration", "account_creation": {"title": "Account Creation", "content": "To access certain features of the service, you need to create an account. You must provide accurate and complete information during the registration process."}, "account_security": {"title": "Account Security", "content": "You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account."}, "user_responsibilities": {"title": "User Responsibilities", "content": "You agree to notify us immediately of any unauthorized use of your account or any other breach of security."}}, "intellectual_property": {"title": "Content and Intellectual Property Rights", "content": "All content provided through GTPlanner, including but not limited to AI planning features, code, documentation generation, and templates, is protected under copyright law. The copyright owner of GTPlanner is vibecoding.sop.best.", "ownership": "You acknowledge that you do not own the underlying technology or intellectual property that makes up the GTPlanner service, and you agree to respect the intellectual property rights of vibecoding.sop.best and any third parties.", "user_content": "While you retain ownership of your custom implementations and modifications, the core GTPlanner platform and features remain the property of vibecoding.sop.best."}, "prohibited_activities": {"title": "Prohibited Activities", "intro": "You agree not to engage in any of the following prohibited activities while using GTPlanner:", "items": {"0": "Unauthorized access to or distribution of our proprietary code and templates", "1": "Reselling or redistributing TAB templates without authorization", "2": "Interfering with or disrupting the security or performance of the service", "3": "Using the service for any illegal or unauthorized purpose", "4": "Attempting to bypass any security features of the service"}}, "privacy": {"title": "Privacy and Data Collection", "intro": "GTPlanner collects the following types of data:", "account_info": "Account Information", "account_info_desc": "Information you provide when creating an account", "usage_details": "Usage Details", "usage_details_desc": "Data related to your activity on our service", "device_info": "Device Information", "device_info_desc": "Information about the device you use to access our service", "cookies": "Cookies", "cookies_desc": "Data that helps us enhance your experience with our service", "payment_info": "Payment and Billing Information", "payment_info_desc": "Data necessary to process payments", "policy_link": "For more details on data collection practices, please refer to our separate"}, "pricing": {"title": "Pricing and Payments", "final_purchases": "All purchases are final and non-refundable unless otherwise required by law", "price_changes": "Prices are subject to change with notice to users", "payment_agreement": "You agree to pay all charges associated with your selected plan", "payment_terms": "Payment terms are based on your selected payment method and plan"}, "termination": {"title": "Termination", "content": "We reserve the right to terminate or suspend your access to the service at our sole discretion, without notice, for conduct that we believe violates these Terms or is harmful to other users of the service, us, or third parties."}, "disclaimer": {"title": "Disclaimer of Warranties", "content": "The service is provided on an \"as is\" and \"as available\" basis. We make no warranties or representations about the accuracy, reliability, or availability of the service and disclaim all warranties to the fullest extent permitted by law."}, "limitation": {"title": "Limitation of Liability", "content": "To the fullest extent permitted by law, vibecoding.sop.best shall not be liable for any direct, indirect, incidental, special, consequential, or punitive damages arising from the use of or inability to use the service."}, "indemnification": {"title": "Indemnification", "content": "You agree to indemnify and hold harmless vibecoding.sop.best, its affiliates, and their respective officers, directors, employees, and agents from any claims, damages, losses, liabilities, and expenses (including attorneys' fees) arising from your use of the service or violation of these Terms."}, "governing_law": {"title": "Governing Law and Dispute Resolution", "content": "These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which vibecoding.sop.best operates, without regard to its conflict of law provisions. Any disputes arising from these Terms or the service will be resolved through binding arbitration in accordance with applicable laws."}, "changes": {"title": "Changes to These Terms", "content": "We reserve the right to update or modify these Terms at any time. Changes will be effective immediately upon posting on our website. Your continued use of the service after any changes signifies your acceptance of the new terms."}, "contact": {"title": "Contact Information", "content": "If you have any questions about these Terms, please contact us at"}, "acknowledgment": "By using GTPlanner, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. Thank you for choosing GTPlanner!"}, "privacy_policy": {"title": "Privacy Policy", "description": "Privacy Policy for GTPlanner - Learn how we collect, use, and protect your personal information", "introduction": {"title": "Introduction", "content": "Welcome to GTPlanner, the AI-powered planning tool designed to help modern developers plan and document their projects efficiently. At GTPlanner, your privacy is of utmost importance to us, and this privacy policy outlines how we collect, use, and protect your personal information when you use our services."}, "information_collection": {"title": "Information Collection and Use", "intro": "We collect the following types of data to provide you with a better experience while using GTPlanner:"}, "account_information": {"title": "1. Account Information", "what_we_collect": "What We Collect", "what_we_collect_desc": "This includes your name, email address, and any other information you provide when creating an account.", "purpose": "Purpose", "purpose_desc": "To manage your account and provide customer support."}, "usage_data": {"title": "2. Usage Data", "what_we_collect": "What We Collect", "what_we_collect_desc": "Information about how you interact with our service, including pages visited, features used, and time spent on the platform.", "purpose": "Purpose", "purpose_desc": "To improve our service and understand user preferences."}, "device_information": {"title": "3. <PERSON>ce Information", "what_we_collect": "What We Collect", "what_we_collect_desc": "Information about the device you use to access GTPlanner, including IP address, browser type, operating system, and device identifiers.", "purpose": "Purpose", "purpose_desc": "To ensure compatibility and security of our service."}, "cookies": {"title": "4. Cookies and Tracking Technologies", "what_we_collect": "What We Collect", "what_we_collect_desc": "We use cookies and similar tracking technologies to enhance your experience and analyze usage patterns.", "purpose": "Purpose", "purpose_desc": "To remember your preferences, maintain your session, and improve our service."}, "payment_information": {"title": "5. Payment and Billing Information", "what_we_collect": "What We Collect", "what_we_collect_desc": "Payment details necessary to process transactions, including billing address and payment method information.", "purpose": "Purpose", "purpose_desc": "To process payments and maintain billing records."}, "data_sharing": {"title": "Data Sharing and Disclosure", "intro": "We do not sell, trade, or otherwise transfer your personal information to third parties except in the following circumstances:", "service_providers": "Service Providers", "service_providers_desc": "We may share information with trusted third-party service providers who assist us in operating our service, conducting our business, or serving our users.", "legal_requirements": "Legal Requirements", "legal_requirements_desc": "We may disclose your information when required by law or to protect our rights, property, or safety.", "business_transfers": "Business Transfers", "business_transfers_desc": "In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the transaction."}, "data_security": {"title": "Data Security", "content": "We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security."}, "data_retention": {"title": "Data Retention", "content": "We retain your personal information for as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements. When your information is no longer needed, we will securely delete or anonymize it."}, "user_rights": {"title": "Your Rights and Choices", "intro": "Depending on your location, you may have the following rights regarding your personal information:", "access": "Access", "access_desc": "Request access to the personal information we hold about you.", "correction": "Correction", "correction_desc": "Request correction of inaccurate or incomplete information.", "deletion": "Deletion", "deletion_desc": "Request deletion of your personal information, subject to certain exceptions.", "portability": "Portability", "portability_desc": "Request a copy of your personal information in a structured, machine-readable format.", "objection": "Objection", "objection_desc": "Object to the processing of your personal information in certain circumstances."}, "international_transfers": {"title": "International Data Transfers", "content": "Your information may be transferred to and processed in countries other than your own. We ensure that such transfers comply with applicable data protection laws and implement appropriate safeguards to protect your information."}, "children_privacy": {"title": "Children's Privacy", "content": "Our service is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we become aware that we have collected personal information from a child under 13, we will take steps to delete such information."}, "policy_changes": {"title": "Changes to This Privacy Policy", "content": "We may update this privacy policy from time to time to reflect changes in our practices or applicable laws. We will notify you of any material changes by posting the updated policy on our website and updating the effective date. Your continued use of our service after such changes constitutes acceptance of the updated policy."}, "contact": {"title": "Contact Information", "content": "If you have any questions or concerns about this privacy policy or our data practices, please contact us at:", "copyright_owner": "Copyright Owner", "email": "Email"}, "consent": "By using GTPlanner, you consent to our privacy policy and agree to its terms. Thank you for trusting us with your information!"}}}