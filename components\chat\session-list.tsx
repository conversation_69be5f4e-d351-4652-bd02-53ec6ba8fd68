"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { ShareDialog } from "./share-dialog";
import {
  Plus,
  MessageSquare,
  MoreHorizontal,
  Edit3,
  Trash2,
  Search,
  PanelLeftClose,
  Share2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ChatSession {
  id: number;
  uuid: string;
  title: string;
  created_at: string;
  updated_at: string;
  status: string;
  chat_state: string;
  is_public: boolean;
}

interface SessionListProps {
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onToggleVisibility?: () => void;
  isNewChatPreparing?: boolean; // Whether in new chat preparation state
  className?: string;
  hideTitleOnMobile?: boolean; // Whether to hide title on mobile
  refreshTrigger?: number; // Used to trigger session list refresh
}

export default function SessionList({
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onToggleVisibility,
  isNewChatPreparing = false,
  className,
  hideTitleOnMobile = false,
  refreshTrigger,
}: SessionListProps) {
  const t = useTranslations('chat');
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [sessionToShare, setSessionToShare] = useState<ChatSession | null>(null);

  // Load session list
  const loadSessions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/chat/sessions');
      if (response.ok) {
        const result = await response.json();
        setSessions(result.data || []);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Prepare new session (don't create immediately)
  const handleNewSession = () => {
    onNewSession(); // Call parent component's new session handler, enter new chat preparation state
  };

  // Rename session
  const handleRenameSession = async (sessionId: string, newTitle: string) => {
    try {
      const response = await fetch(`/api/chat/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newTitle,
        }),
      });

      if (response.ok) {
        setSessions(prev =>
          prev.map(session =>
            session.uuid === sessionId
              ? { ...session, title: newTitle }
              : session
          )
        );
        setEditingSessionId(null);
        setEditingTitle("");
      }
    } catch (error) {
      console.error('Failed to rename session:', error);
    }
  };

  // 打开删除确认对话框
  const openDeleteDialog = (sessionId: string) => {
    setSessionToDelete(sessionId);
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSessionToDelete(null);
  };

  // 确认删除会话
  const confirmDeleteSession = async () => {
    if (!sessionToDelete) return;

    try {
      const response = await fetch(`/api/chat/sessions/${sessionToDelete}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSessions(prev => prev.filter(session => session.uuid !== sessionToDelete));
        // 如果删除的是当前会话，创建新会话
        if (currentSessionId === sessionToDelete) {
          handleNewSession();
        }
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    } finally {
      closeDeleteDialog();
    }
  };

  // 切换会话分享状态
  const handleToggleShare = async (sessionId: string, currentShareState: boolean) => {
    try {
      const response = await fetch(`/api/chat/sessions/${sessionId}/share`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_public: !currentShareState,
        }),
      });

      if (response.ok) {
        // 更新本地状态
        setSessions(prev =>
          prev.map(session =>
            session.uuid === sessionId
              ? { ...session, is_public: !currentShareState }
              : session
          )
        );
        
        // 更新分享弹窗中的会话状态
        if (sessionToShare && sessionToShare.uuid === sessionId) {
          setSessionToShare(prev => 
            prev ? { ...prev, is_public: !currentShareState } : null
          );
        }
        
        // 显示成功提示
        const message = !currentShareState 
          ? t('session_list.share_success') 
          : t('session_list.unshare_success');
        toast.success(message);
      } else {
        throw new Error('Failed to update share status');
      }
    } catch (error) {
      console.error('Failed to toggle share status:', error);
      throw error; // 重新抛出错误，让分享弹窗可以处理
    }
  };



  // 获取时间分组标签
  const getTimeGroupLabel = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInHours < 24) {
      return t('session_list.time_groups.today');
    } else if (diffInDays === 1) {
      return t('session_list.time_groups.yesterday');
    } else if (diffInDays <= 7) {
      return t('session_list.time_groups.this_week');
    } else if (diffInDays <= 30) {
      return t('session_list.time_groups.this_month');
    } else {
      return t('session_list.time_groups.earlier');
    }
  };

  // 过滤和排序会话（最新的在上面）
  const filteredSessions = sessions
    .filter(session =>
      session.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      // 按 updated_at 降序排序（最新的在前）
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    });

  // 按时间分组会话
  const groupedSessions = filteredSessions.reduce((groups, session) => {
    const groupLabel = getTimeGroupLabel(session.updated_at);
    if (!groups[groupLabel]) {
      groups[groupLabel] = [];
    }
    groups[groupLabel].push(session);
    return groups;
  }, {} as Record<string, ChatSession[]>);

  // 定义分组顺序
  const groupOrder = [
    t('session_list.time_groups.today'),
    t('session_list.time_groups.yesterday'),
    t('session_list.time_groups.this_week'),
    t('session_list.time_groups.this_month'),
    t('session_list.time_groups.earlier')
  ];



  useEffect(() => {
    loadSessions();
  }, [refreshTrigger]);

  // 当当前会话改变时，重置编辑状态
  useEffect(() => {
    setEditingSessionId(null);
    setEditingTitle("");
  }, [currentSessionId]);

  // 当搜索查询改变时，重置编辑状态
  useEffect(() => {
    if (searchQuery) {
      setEditingSessionId(null);
      setEditingTitle("");
    }
  }, [searchQuery]);

  return (
    <div className={cn("flex flex-col h-full bg-gradient-to-b from-muted/20 to-muted/10 backdrop-blur-sm", className)}>
      {/* 头部 */}
      <div className="p-4 border-b border-border/30 bg-background/50 backdrop-blur-sm">
        {!hideTitleOnMobile && (
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-bold text-lg bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('session_list.title')}
            </h2>
            <div className="flex items-center gap-2">
              {onToggleVisibility && (
                <Button
                  onClick={onToggleVisibility}
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 rounded-xl hover:bg-muted/60 transition-all duration-200"
                  title={t('session_list.hide_list_tooltip')}
                >
                  <PanelLeftClose className="h-4 w-4" />
                </Button>
              )}
              <Button
                onClick={handleNewSession}
                size="sm"
                className={`h-9 w-9 p-0 rounded-xl transition-all duration-200 ${
                  isNewChatPreparing
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
                }`}
                title={isNewChatPreparing ? t('session_list.new_chat_preparing') : t('session_list.new_chat_tooltip')}
                disabled={isNewChatPreparing}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* 移动端：搜索框和新建会话按钮在同一行 */}
        {hideTitleOnMobile ? (
          <div className="flex gap-3 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground/70" />
              <Input
                placeholder={t('session_list.search_placeholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10 rounded-xl border-border/40 bg-background/60 hover:bg-background/80 focus:bg-background transition-all duration-200"
              />
            </div>
            <Button
              onClick={handleNewSession}
              size="sm"
              className={`h-10 w-10 p-0 shrink-0 rounded-xl transition-all duration-200 ${
                isNewChatPreparing
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
              }`}
              title={isNewChatPreparing ? t('session_list.new_chat_preparing') : t('session_list.new_chat_tooltip')}
              disabled={isNewChatPreparing}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          /* 桌面端：搜索框单独一行 */
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground/70" />
            <Input
              placeholder={t('session_list.search_placeholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-10 rounded-xl border-border/40 bg-background/60 hover:bg-background/80 focus:bg-background transition-all duration-200 shadow-sm focus:shadow-md"
            />
          </div>
        )}
      </div>

      {/* 会话列表 */}
      <div className="flex-1 overflow-y-auto session-list-scroll">
        {loading ? (
          <div className="p-6 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
              <span className="text-muted-foreground text-sm">{t('session_list.loading')}</span>
            </div>
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="p-6 text-center">
            <div className="flex flex-col items-center gap-3">
              <MessageSquare className="w-12 h-12 text-muted-foreground/30" />
              <span className="text-muted-foreground text-sm">
                {searchQuery ? t('session_list.no_search_results') : t('session_list.no_sessions')}
              </span>
            </div>
          </div>
        ) : (
          <div className="p-3">
            {groupOrder.map((groupLabel) => {
              const groupSessions = groupedSessions[groupLabel];
              if (!groupSessions || groupSessions.length === 0) return null;

              return (
                <div key={groupLabel} className="mb-6 last:mb-0">
                  {/* 分组标题 */}
                  <div className="px-2 py-2 mb-3">
                    <h4 className="text-xs font-medium text-muted-foreground/80 uppercase tracking-wider">
                      {groupLabel}
                    </h4>
                  </div>

                  {/* 该分组的会话列表 */}
                  <div className="space-y-2">
                    {groupSessions.map((session) => (
                      <Card
                        key={session.uuid}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:shadow-md group session-item border-border/40",
                          currentSessionId === session.uuid
                            ? "bg-gradient-to-r from-primary/10 to-primary/5 border-primary/50 shadow-md"
                            : "hover:bg-muted/40 hover:border-border/60"
                        )}
                        onClick={() => {
                          // 如果当前有其他会话在编辑状态，先重置编辑状态
                          if (editingSessionId && editingSessionId !== session.uuid) {
                            setEditingSessionId(null);
                            setEditingTitle("");
                          }
                          onSessionSelect(session.uuid);
                        }}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                              <div className={cn(
                                "p-1.5 rounded-lg transition-colors flex-shrink-0",
                                currentSessionId === session.uuid
                                  ? "bg-primary/20 text-primary"
                                  : "bg-muted/60 text-muted-foreground group-hover:bg-muted group-hover:text-foreground"
                              )}>
                                <MessageSquare className="h-4 w-4" />
                              </div>
                              {editingSessionId === session.uuid ? (
                                <Input
                                  value={editingTitle}
                                  onChange={(e) => setEditingTitle(e.target.value)}
                                  onBlur={() => {
                                    if (editingTitle.trim() && editingTitle.trim() !== session.title) {
                                      handleRenameSession(session.uuid, editingTitle.trim());
                                    } else {
                                      setEditingSessionId(null);
                                      setEditingTitle("");
                                    }
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      if (editingTitle.trim() && editingTitle.trim() !== session.title) {
                                        handleRenameSession(session.uuid, editingTitle.trim());
                                      } else {
                                        setEditingSessionId(null);
                                        setEditingTitle("");
                                      }
                                    } else if (e.key === 'Escape') {
                                      setEditingSessionId(null);
                                      setEditingTitle("");
                                    }
                                  }}
                                  className="h-7 text-sm rounded-lg flex-1"
                                  autoFocus
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <h3 className="font-semibold text-sm truncate group-hover:text-foreground transition-colors flex-1">
                                  {session.title}
                                </h3>
                              )}
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/60 rounded-lg flex-shrink-0"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="rounded-xl border-border/50 shadow-lg">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingSessionId(session.uuid);
                                    setEditingTitle(session.title);
                                  }}
                                  className="rounded-lg hover:bg-muted/60 transition-colors"
                                >
                                  <Edit3 className="h-4 w-4 mr-3" />
                                  {t('session_list.rename')}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSessionToShare(session);
                                    setShareDialogOpen(true);
                                  }}
                                  className="rounded-lg hover:bg-muted/60 transition-colors"
                                >
                                  <Share2 className="h-4 w-4 mr-3" />
                                  {t('session_list.share')}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openDeleteDialog(session.uuid);
                                  }}
                                  className="text-destructive rounded-lg hover:bg-destructive/10 transition-colors"
                                >
                                  <Trash2 className="h-4 w-4 mr-3" />
                                  {t('session_list.delete')}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              {t('session_list.delete_confirm_title')}
            </DialogTitle>
            <DialogDescription className="text-left">
              {t('session_list.delete_confirm_description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2 sm:gap-2">
            <Button
              variant="outline"
              onClick={closeDeleteDialog}
              className="flex-1"
            >
              {t('session_list.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteSession}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t('session_list.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分享对话框 */}
      {sessionToShare && (
        <ShareDialog
          open={shareDialogOpen}
          onOpenChange={setShareDialogOpen}
          sessionId={sessionToShare.uuid}
          sessionTitle={sessionToShare.title}
          isPublic={sessionToShare.is_public}
          onToggleShare={handleToggleShare}
        />
      )}

    </div>
  );
}
