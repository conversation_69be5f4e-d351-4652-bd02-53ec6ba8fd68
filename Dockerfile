# Multi-stage build for GTPlanner Frontend
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Accept build argument for environment
ARG BUILD_ENV=production

# Copy the appropriate environment file
RUN if [ "$BUILD_ENV" = "development" ]; then \
        cp .env.development .env.local; \
    else \
        cp .env.production .env.local; \
    fi
RUN rm .env.development .env.production

# Build the application
RUN pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create nextjs user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the standalone build
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Copy database migration files and scripts
COPY --from=builder /app/db ./db
COPY --from=builder /app/package.json ./package.json

# Copy node_modules from builder stage for migration tools
COPY --from=builder /app/node_modules ./node_modules

# Change ownership of the app directory

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Create entrypoint script for handling migrations
COPY --chown=nextjs:nodejs <<EOF /app/entrypoint.sh
#!/bin/sh
set -e

# Check if this is a migration container (init container)
if [ "\$1" = "migrate" ]; then
    echo "Running database migrations..."
    npm run db:migrate
    echo "Migrations completed"
    exit 0
fi

# Default: start the Next.js server
echo "Starting Next.js server..."
exec node server.js
EOF

RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
CMD []
