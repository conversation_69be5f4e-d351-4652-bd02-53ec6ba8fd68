import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { chatSessions, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(
  request: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取用户的 uuid
    const userResult = await db()
      .select({ uuid: users.uuid })
      .from(users)
      .where(eq(users.email, session.user.email))
      .limit(1);

    if (userResult.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userUuid = userResult[0].uuid;

    const { is_public } = await request.json();
    const sessionUuid = params.uuid;

    // 验证输入
    if (typeof is_public !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid is_public value' },
        { status: 400 }
      );
    }

    // 检查会话是否存在且属于当前用户
    const existingSessions = await db()
      .select()
      .from(chatSessions)
      .where(
        and(
          eq(chatSessions.uuid, sessionUuid),
          eq(chatSessions.user_uuid, userUuid)
        )
      )
      .limit(1);

    if (existingSessions.length === 0) {
      return NextResponse.json(
        { error: 'Session not found or access denied' },
        { status: 404 }
      );
    }

    // 更新会话的分享状态
    await db()
      .update(chatSessions)
      .set({
        is_public,
        updated_at: new Date(),
      })
      .where(
        and(
          eq(chatSessions.uuid, sessionUuid),
          eq(chatSessions.user_uuid, userUuid)
        )
      );

    // 返回更新后的会话信息
    const updatedSessions = await db()
      .select()
      .from(chatSessions)
      .where(eq(chatSessions.uuid, sessionUuid))
      .limit(1);

    return NextResponse.json({
      success: true,
      data: updatedSessions[0],
    });

  } catch (error) {
    console.error('Error updating session share status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 