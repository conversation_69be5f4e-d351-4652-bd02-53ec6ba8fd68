"use client";
import { useEffect } from "react";
import { cn } from "@/lib/utils";

import { useTranslations } from 'next-intl';
import SessionList from '@/components/chat/session-list';
import CollapsedSessionList from '@/components/chat/collapsed-session-list';
import Header from '@/components/blocks/header';
import { Header as HeaderType } from "@/types/blocks/header";
import { ChatState } from '@/types/conversation';

// 导入拆分后的hooks和服务
import { useChatState } from '@/hooks/useChatState';
import { useSessionManager } from '@/hooks/useSessionManager';
import { useConversationFlow } from '@/hooks/useConversationFlow';
import { chatHelpers } from '@/utils/chatHelpers';

// 导入拆分后的组件
import { MessageList } from '@/components/chat/messages/message-list';
import { StreamingMessage } from '@/components/chat/messages/streaming-message';
import { ChatInput } from '@/components/chat/input/chat-input';
import { DesktopDocumentPanel } from '@/components/chat/document/desktop-document-panel';
import { MobileDocumentPanel } from '@/components/chat/document/mobile-document-panel';
import { WelcomePage } from '@/components/chat/layout/welcome-page';
import { ConfirmCard } from '@/components/chat/layout/confirm-card';
import { DocumentCard } from '@/components/chat/layout/document-card';
import { MobileSessionPanel } from '@/components/chat/layout/mobile-session-panel';

import './chat.css';








export default function ChatClient({ params, header }: { params: { locale: string }, header?: HeaderType }) {
  const t = useTranslations('chat');
  const locale = params.locale;

  // 使用拆分后的hooks
  const chatState = useChatState();
  const sessionManager = useSessionManager(chatState.messages);
  const conversationFlow = useConversationFlow();





  // 国际化的示例数据
  const examples = chatHelpers.EXAMPLE_IDS.map((id: string) => ({
    id,
    title: t(`examples.${id}.title`),
    description: t(`examples.${id}.description`),
    prompt: t(`examples.${id}.prompt`)
  }));

  // 国际化错误处理
  const getLocalizedError = (error: string) => chatHelpers.getLocalizedError(error, t);



  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      chatState.setIsMobile(chatHelpers.checkMobile());
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 初始化时设置新对话准备状态
  useEffect(() => {
    sessionManager.setIsNewChatPreparing(sessionManager.isInNewChatState());
  }, [sessionManager.currentSessionId, chatState.messages]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + B 切换会话列表
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        if (!chatState.isMobile) {
          sessionManager.toggleSessionList(chatState.isMobile);
        }
      }
      // Escape 关闭移动端会话列表
      if (e.key === 'Escape' && chatState.isMobile && sessionManager.sessionListCollapsed) {
        sessionManager.setSessionListCollapsed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [chatState.isMobile, sessionManager.sessionListCollapsed, sessionManager.showSessionList]);

  // 自动滚动到底部
  useEffect(() => {
    if (chatState.messages.length > 0 || chatState.streamingMessage) {
      chatHelpers.scrollToBottom();
    }
  }, [chatState.messages, chatState.streamingMessage]);

  // 添加消息到对话历史
  const addMessage = async (content: string, role: 'user' | 'assistant', type?: 'plan' | 'message' | 'document' | 'analysis', sessionId?: string) => {
    const newMessage = {
      id: Date.now().toString(),
      content,
      role,
      timestamp: Date.now(),
      type: type || 'message'
    };
    chatState.setMessages((prev: any[]) => [...prev, newMessage]);

    // 保存消息到数据库（使用传入的sessionId或当前sessionId）
    const targetSessionId = sessionId || sessionManager.currentSessionId;
    if (targetSessionId) {
      await sessionManager.saveMessageToSession(targetSessionId, role, content, type);
    }
  };

  // 加载会话消息
  const loadSessionMessages = async (sessionId: string) => {
    const messages = await sessionManager.loadSessionMessages(sessionId);
    chatState.setMessages(messages);
    // 根据消息恢复会话状态
    chatHelpers.restoreSessionState(
      messages,
      chatState.setState,
      chatState.setShowDocPanel,
      chatState.setIsEditingPlan,
      chatState.setEditablePlan,
      chatState.setEditableDoc,
      chatState.setIsViewingHistoryDoc,
      chatState.setCurrentRequirement,
      chatState.setShortPlanResult,
      chatState.setLongDocResult
    );
  };

  // 处理会话选择
  const handleSessionSelect = async (sessionId: string) => {
    sessionManager.setCurrentSessionId(sessionId);
    sessionManager.setIsNewChatPreparing(false); // 退出新对话准备状态
    await loadSessionMessages(sessionId);
  };

  // 处理新建会话（准备新对话状态）
  const handleNewSession = () => {
    sessionManager.setCurrentSessionId(null);
    chatState.setMessages([]);
    chatState.setState(ChatState.WELCOME);
    chatState.setCurrentRequirement("");
    chatState.setShortPlanResult("");
    chatState.setLongDocResult("");
    chatState.setShowDocPanel(false);
    chatState.setIsEditingPlan(false);
    chatState.setEditablePlan("");
    chatState.setEditableDoc("");
    chatState.setIsViewingHistoryDoc(false);
    sessionManager.setIsNewChatPreparing(true);

    // 聚焦到输入框（延迟执行确保DOM已更新）
    setTimeout(() => {
      const inputElement = document.querySelector('textarea[placeholder*="输入"]') as HTMLTextAreaElement;
      if (inputElement) {
        inputElement.focus();
      }
    }, 100);
  };



  // 处理示例点击
  const handleExampleClick = async (example: typeof examples[0]) => {
    const sessionId = await sessionManager.createNewSessionIfNeeded(example.prompt, example.title);
    chatState.setCurrentRequirement(example.prompt);
    chatState.setUserInput("");
    await addMessage(example.prompt, 'user', 'message', sessionId || undefined);
    await handleUnifiedConversationFlow(example.prompt, sessionId || undefined);
  };


  // 处理流式对话
  const handleUnifiedConversationFlow = async (input: string, sessionId?: string) => {
    await conversationFlow.handleUnifiedConversationFlow(
      input,
      sessionId,
      chatState.messages,
      chatState.shortPlanResult,
      chatState.longDocResult,
      locale,
      {
        setState: chatState.setState,
        setLoadingStartTime: chatState.setLoadingStartTime,
        setStreamingMessage: chatState.setStreamingMessage,
        setStreamingPlan: chatState.setStreamingPlan,
        setStreamingDoc: chatState.setStreamingDoc,
        setStreamingAnalysis: chatState.setStreamingAnalysis,
        setShortPlanResult: chatState.setShortPlanResult,
        setEditablePlan: chatState.setEditablePlan,
        setLongDocResult: chatState.setLongDocResult,
        setEditableDoc: chatState.setEditableDoc,
        setShowDocPanel: chatState.setShowDocPanel,
        addMessage,
        getLocalizedError,
      }
    );
  };

  // 处理文档生成按钮点击
  const handleGenerateDocument = async () => {
    await conversationFlow.handleGenerateDocument(
      chatState.shortPlanResult,
      sessionManager.currentSessionId,
      chatState.messages,
      chatState.longDocResult,
      locale,
      {
        setState: chatState.setState,
        setStreamingMessage: chatState.setStreamingMessage,
        setStreamingDoc: chatState.setStreamingDoc,
        setStreamingAnalysis: chatState.setStreamingAnalysis,
        setLongDocResult: chatState.setLongDocResult,
        setEditableDoc: chatState.setEditableDoc,
        setShowDocPanel: chatState.setShowDocPanel,
        addMessage,
      }
    );
  };

  // 处理用户输入提交
  const handleSubmit = async () => {
    if (!chatState.userInput.trim()) return;

    const input = chatState.userInput.trim();
    chatState.setUserInput("");

    // 确保有会话存在（在新对话准备状态时创建会话）
    const sessionId = await sessionManager.createNewSessionIfNeeded(input);

    // 添加消息到本地状态和数据库
    await addMessage(input, 'user', 'message', sessionId || undefined);

    // 使用统一对话处理所有情况
    await handleUnifiedConversationFlow(input, sessionId || undefined);
  };

  // 修改处理手动编辑的函数
  const handleEditPlan = () => {
    if (chatState.isEditingPlan) {
      // 如果当前是编辑状态，保存更改
      chatState.setShortPlanResult(chatState.editablePlan);
      // 更新现有的规划消息而不是添加新消息
      updatePlanMessage(chatState.editablePlan);
    }
    chatState.setIsEditingPlan(!chatState.isEditingPlan);
  };

  // 更新现有的规划消息
  const updatePlanMessage = async (newPlanContent: string) => {
    if (!sessionManager.currentSessionId) return;

    // 更新本地消息状态
    chatState.setMessages((prev: any[]) => prev.map((msg: any) =>
      msg.type === 'plan' ? { ...msg, content: newPlanContent } : msg
    ));

    // 更新数据库中的规划消息
    try {
      // 找到规划消息的ID
      const planMessage = chatState.messages.find((msg: any) => msg.type === 'plan');
      if (planMessage) {
        await sessionManager.updateMessageInSession(sessionManager.currentSessionId, planMessage.id, newPlanContent, 'plan');
      }
    } catch (error) {
      console.error('Failed to update plan message:', error);
    }
  };





  // 渲染欢迎页面
  const renderWelcome = () => (
    <WelcomePage
      examples={examples}
      onExampleClick={handleExampleClick}
      t={t}
    />
  );

  // 渲染消息列表
  const renderMessages = (showPlans = true) => (
    <MessageList
      messages={chatState.messages}
      showPlans={showPlans}
      locale={locale}
      t={t}
      onViewDocument={(content) => {
        chatState.setEditableDoc(content);
        chatState.setIsViewingHistoryDoc(true);
        chatState.setShowDocPanel(true);
      }}
    />
  );

  // 主内容渲染逻辑 - 重构后的清晰状态判断
  const renderMainContent = () => {
    switch (chatState.state) {
      case ChatState.WELCOME:
        return renderWelcome();

      case ChatState.CONVERSATION:
      case ChatState.LOADING:
        return (
          <div className="space-y-6 px-2">
            {/* 显示对话消息，包括规划消息 */}
            {renderMessages(true)}
            {/* 显示流式消息 */}
            {chatState.streamingMessage && (
              <div className="mt-4">
                <StreamingMessage content={chatState.streamingMessage} />
              </div>
            )}
            {/* 显示流式规划 */}
            {chatState.streamingPlan && (
              <div className="mt-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-primary">💡</span>
                  </div>
                  <p className="text-lg">{t('messages.plan_title')}</p>
                </div>
                <div className="bg-muted/30 border border-border/50 rounded-lg p-6">
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap">{chatState.streamingPlan}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case ChatState.SHORT_LOADING:
        return (
          <div className="space-y-6 px-2">
            {/* 显示聊天历史，移除加载指示器，直接显示流式内容 */}
            {renderMessages(true)}
          </div>
        );

      case ChatState.SHORT_CONFIRM:
      case ChatState.SHORT_CHAT_LOADING:
        return (
          <div className="space-y-6 px-2">
            {renderConfirmCard()}
          </div>
        );

      case ChatState.LONG_LOADING:
        return (
          <div className="space-y-6 px-2">
            {/* 显示确认卡片，包含完整的对话历史和规划 */}
            {renderConfirmCard()}

            {/* 思考过程面板应该只在设计文档面板中显示，不在对话列表中显示 */}
          </div>
        );

      case ChatState.LONG_RESULT:
        return (
          <div className="space-y-6 px-2">
            {/* 显示确认卡片，包含完整的对话历史和规划 */}
            {renderConfirmCard()}

            {/* 状态指示器和文档卡片 */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                  <span className="text-green-500">✅</span>
                </div>
                <p className="text-lg">{t('messages.design_completed')}</p>
              </div>
              {/* 只有在文档生成完成时才显示文档卡片 */}
              {renderDocumentCard()}
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-6 px-2">
            {renderMessages(true)}
          </div>
        );
    }
  };

  // 渲染确认卡片
  const renderConfirmCard = () => (
    <ConfirmCard
      messages={chatState.messages}
      streamingMessage={chatState.streamingMessage}
      isEditingPlan={chatState.isEditingPlan}
      editablePlan={chatState.editablePlan}
      onEditablePlanChange={chatState.setEditablePlan}
      onEditPlan={handleEditPlan}
      onGenerateDocument={handleGenerateDocument}
      state={chatState.state}
      isMobile={chatState.isMobile}
      locale={locale}
      t={t}
      onViewDocument={(content) => {
        chatState.setEditableDoc(content);
        chatState.setIsViewingHistoryDoc(true);
        chatState.setShowDocPanel(true);
      }}
    />
  );

  // 渲染长文档结果卡片
  const renderDocumentCard = () => (
    <DocumentCard
      showDocPanel={chatState.showDocPanel}
      onOpenPanel={() => chatState.setShowDocPanel(true)}
      t={t}
    />
  );

  // 主布局
  const renderMainLayout = () => (
    <div className="h-full bg-gradient-to-br from-background via-background to-muted/20">
      {/* 主要内容区域 */}
      <div className={cn(
        "flex h-full",
        chatState.isMobile && "flex-col"
      )}>
        {/* 左侧：会话列表 */}
        {sessionManager.showSessionList && !chatState.isMobile && (
          <>
            {sessionManager.sidebarCollapsed ? (
              <CollapsedSessionList
                onNewSession={handleNewSession}
                onExpand={() => sessionManager.setSidebarCollapsed(false)}
                className="session-list-transition"
              />
            ) : (
              <div className="w-[280px] border-r bg-muted/30 session-list-transition session-list-desktop flex-shrink-0">
                <SessionList
                  currentSessionId={sessionManager.currentSessionId || undefined}
                  onSessionSelect={handleSessionSelect}
                  onNewSession={handleNewSession}
                  onToggleVisibility={() => sessionManager.toggleSessionList(chatState.isMobile)}
                  isNewChatPreparing={sessionManager.isInNewChatState()}
                  className="session-list-scroll"
                  refreshTrigger={sessionManager.sessionListRefreshTrigger}
                />
              </div>
            )}
          </>
        )}

        {/* 右侧：column 布局（header + 聊天区域 + 输入框） */}
        <div className="flex-1 flex flex-col min-w-0 relative h-full">

          {/* 头部导航 - 固定不滚动 */}
          {header && (
            <div className="shrink-0">
              <Header
                header={header}
                showMobileSessionButton={chatState.isMobile}
                onShowMobileSessionList={() => sessionManager.setSessionListCollapsed(true)}
              />
            </div>
          )}

          {/* 中间：row 布局（聊天记录 + Design Document） - 可滚动 */}
          <div className="flex-1 flex overflow-hidden min-h-0">
            {/* 聊天记录区域 - 1/3 宽度 */}
            <div className={cn(
              "flex flex-col transition-all duration-800 ease-in-out relative min-w-0",
              chatState.showDocPanel && !chatState.isMobile ? "flex-[1]" : "flex-1"
            )}>

              <div className="flex-1 p-4 md:p-6 overflow-auto chat-scroll-area">
                <div className="w-full min-h-full">
                  {renderMainContent()}
                </div>
              </div>

              {/* 底部输入框 - 统一使用内联布局 */}
              <div className="shrink-0">
                {renderInlineInput()}
              </div>
            </div>

            {/* Design Document 面板 */}
            <DesktopDocumentPanel
              show={chatState.showDocPanel && !chatState.isMobile}
              content={chatState.editableDoc}
              onChange={chatState.setEditableDoc}
              isMarkdownPreview={chatState.isMarkdownPreview}
              onTogglePreview={chatState.setIsMarkdownPreview}
              onClose={() => chatState.setShowDocPanel(false)}
              isViewingHistory={chatState.isViewingHistoryDoc}
              onBackToProgress={() => chatState.setIsViewingHistoryDoc(false)}
              state={chatState.state}
              streamingAnalysis={chatState.streamingAnalysis}
              analysisContent={chatState.messages.filter((msg: any) => msg.type === 'analysis').pop()?.content}
              t={t}
            />
          </div>

        </div>
      </div>
    </div>
  );

  // 移动端会话列表面板 - 侧边滑出
  const renderMobileSessionPanel = () => (
    <MobileSessionPanel
      show={sessionManager.sessionListCollapsed && chatState.isMobile}
      currentSessionId={sessionManager.currentSessionId || undefined}
      onSessionSelect={handleSessionSelect}
      onNewSession={handleNewSession}
      onClose={() => sessionManager.setSessionListCollapsed(false)}
      isNewChatPreparing={sessionManager.isInNewChatState()}
      refreshTrigger={sessionManager.sessionListRefreshTrigger}
      t={t}
    />
  );

  // 移动端文档展示面板 - 全屏覆盖
  const renderMobileDocPanel = () => (
    <MobileDocumentPanel
      show={chatState.showDocPanel && chatState.isMobile}
      content={chatState.editableDoc}
      onChange={chatState.setEditableDoc}
      isMarkdownPreview={chatState.isMarkdownPreview}
      onTogglePreview={chatState.setIsMarkdownPreview}
      onClose={() => chatState.setShowDocPanel(false)}
      isViewingHistory={chatState.isViewingHistoryDoc}
      onBackToProgress={() => chatState.setIsViewingHistoryDoc(false)}
      state={chatState.state}
      streamingAnalysis={chatState.streamingAnalysis}
      analysisContent={chatState.messages.filter((msg: any) => msg.type === 'analysis').pop()?.content}
      t={t}
    />
  );



  // 渲染内联输入框（用于移动端column布局）
  const renderInlineInput = () => {
    const isLoading = chatState.state === ChatState.LOADING || chatState.state === ChatState.LONG_LOADING || chatState.streamingMessage !== "";
    const showHelpTooltip = [
      ChatState.SHORT_CONFIRM,
      ChatState.LONG_LOADING,
      ChatState.LONG_RESULT
    ].includes(chatState.state);

    return (
      <ChatInput
        value={chatState.userInput}
        onChange={chatState.setUserInput}
        onSubmit={handleSubmit}
        disabled={isLoading}
        state={chatState.state}
        loadingStartTime={chatState.loadingStartTime}
        t={t}
        showHelpTooltip={showHelpTooltip}
      />
    );
  };



  // 统一返回主布局
  return (
    <>
      {renderMainLayout()}
      {renderMobileSessionPanel()}
      {renderMobileDocPanel()}
    </>
  );
} 