# 流式输出标签系统规范

## 概述

为了支持实时的流式输出和UI组件激活，我们设计了一套基于标签的结构化输出系统。

## 标签格式

```
[TAG_NAME_START]
内容
[TAG_NAME_END]
```

## 支持的标签类型

### 1. 普通文本
```
[TEXT_START]
这是普通的对话文本内容
[TEXT_END]
```

### 2. 短规划卡片
```
[SHORT_PLAN_START]
## 📋 项目规划

### 🔍 需求分析
- 分析用户需求
- 确定核心功能

### 📊 架构设计
- 设计系统架构
- 选择技术栈

### 📝 实施计划
- 制定开发计划
- 分配资源
[SHORT_PLAN_END]
```

### 3. 长文档
```
[LONG_DOC_START]
# 📊 详细设计文档

## 系统概述
详细的系统设计文档内容...

## 技术架构
技术实现细节...
[LONG_DOC_END]
```

### 4. 建议/提示（已废弃）
```
注意：建议和提示内容现在直接包含在TEXT标签内，不再使用单独的SUGGESTION标签。

示例：
[TEXT_START]
您的需求很清楚！

💡 **建议**：您可以考虑添加用户认证功能来提高安全性。

接下来我将为您制定详细的实施计划...
[TEXT_END]
```

### 5. 错误信息
```
[ERROR_START]
❌ 处理请求时出现错误：网络连接超时
[ERROR_END]
```

### 6. 状态信息
```
[STATUS_START]
🔄 正在分析您的需求...
[STATUS_END]
```

## 前端处理逻辑

1. **实时解析**：监听SSE流，实时解析标签
2. **组件激活**：根据标签类型激活对应的UI组件
3. **内容累积**：在标签内累积内容，直到遇到结束标签
4. **渲染更新**：实时更新UI显示

## 示例流式输出

```
[STATUS_START]
正在分析您的需求...
[STATUS_END]

[TEXT_START]
我理解您想要创建一个待办事项应用。这是一个很好的项目想法！
[TEXT_END]

[SHORT_PLAN_START]
## 📋 待办事项应用规划

### 🔍 核心功能
- 任务创建和编辑
- 任务状态管理
- 优先级设置

### 📊 技术架构
- 前端：React + TypeScript
- 后端：Node.js + Express
- 数据库：MongoDB

### 📝 开发步骤
1. 设计数据模型
2. 实现后端API
3. 开发前端界面
4. 测试和部署
[SHORT_PLAN_END]

[TEXT_START]
您觉得这个规划如何？需要我生成详细的设计文档吗？
[TEXT_END]
```

## 优势

1. **实时响应**：用户可以立即看到内容开始生成
2. **结构化显示**：不同类型的内容在不同的UI组件中显示
3. **易于解析**：简单的字符串匹配即可识别标签
4. **扩展性强**：可以轻松添加新的标签类型
5. **向后兼容**：可以与现有的消息系统共存
