#!/bin/bash

# GitHub Secrets Validation Script
# This script helps validate that all required secrets are configured

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  GTPlanner CI/CD Setup Check  ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Required secrets for CI/CD
REQUIRED_SECRETS=(
    "K8S_TOKEN"
    "K8S_API_URL" 
    "NAMESPACE"
    "DEPLOYMENT_NAME"
)

# Optional secrets
OPTIONAL_SECRETS=(
    "DATABASE_URL"
    "AUTH_SECRET"
    "OPENAI_API_KEY"
    "CASDOOR_ENDPOINT"
    "CASDOOR_CLIENT_ID"
    "CASDOOR_CLIENT_SECRET"
)

check_github_cli() {
    if ! command -v gh &> /dev/null; then
        print_error "GitHub CLI (gh) is not installed"
        print_info "Please install it from: ${NEXT_PUBLIC_GITHUB_CLI_URL:-https://cli.github.com/}"
        return 1
    fi
    
    if ! gh auth status &> /dev/null; then
        print_error "GitHub CLI is not authenticated"
        print_info "Please run: gh auth login"
        return 1
    fi
    
    print_success "GitHub CLI is installed and authenticated"
    return 0
}

check_secrets() {
    local repo_url=$(git remote get-url origin 2>/dev/null || echo "")
    if [[ -z "$repo_url" ]]; then
        print_error "Not in a git repository or no origin remote found"
        return 1
    fi
    
    # Extract repo name from URL
    local repo_name=$(echo "$repo_url" | sed -E 's/.*[\/:]([^\/]+\/[^\/]+)\.git$/\1/' | sed 's/\.git$//')
    
    print_info "Checking secrets for repository: $repo_name"
    echo
    
    # Check required secrets
    echo -e "${BLUE}Required Secrets:${NC}"
    local missing_required=0
    
    for secret in "${REQUIRED_SECRETS[@]}"; do
        if gh secret list -R "$repo_name" | grep -q "^$secret"; then
            print_success "$secret is configured"
        else
            print_error "$secret is missing"
            ((missing_required++))
        fi
    done
    
    echo
    
    # Check optional secrets
    echo -e "${BLUE}Optional Secrets:${NC}"
    for secret in "${OPTIONAL_SECRETS[@]}"; do
        if gh secret list -R "$repo_name" | grep -q "^$secret"; then
            print_success "$secret is configured"
        else
            print_warning "$secret is not configured (optional)"
        fi
    done
    
    echo
    
    if [[ $missing_required -gt 0 ]]; then
        print_error "$missing_required required secret(s) are missing"
        echo
        print_info "To set secrets, use:"
        echo "  gh secret set SECRET_NAME -R $repo_name"
        echo "  or visit: https://github.com/$repo_name/settings/secrets/actions"
        return 1
    else
        print_success "All required secrets are configured!"
        return 0
    fi
}

show_setup_instructions() {
    echo
    echo -e "${BLUE}Setup Instructions:${NC}"
    echo
    echo "1. Set required secrets:"
    echo "   gh secret set K8S_TOKEN -R <your-repo>"
    echo "   gh secret set K8S_API_URL -R <your-repo>"
    echo "   gh secret set NAMESPACE -R <your-repo>"
    echo "   gh secret set DEPLOYMENT_NAME -R <your-repo>"
    echo
    echo "2. Example values based on your configuration:"
    echo "   K8S_API_URL: http://kuboard.sensedeal.wiki/k8s-api"
    echo "   NAMESPACE: cubechat"
    echo "   DEPLOYMENT_NAME: gtplanner-frontend"
    echo
    echo "3. For K8S_TOKEN, create a ServiceAccount:"
    echo "   kubectl create serviceaccount gtplanner-deployer -n cubechat"
    echo "   kubectl create clusterrolebinding gtplanner-deployer \\"
    echo "     --clusterrole=edit \\"
    echo "     --serviceaccount=cubechat:gtplanner-deployer"
    echo "   kubectl create token gtplanner-deployer -n cubechat --duration=8760h"
    echo
}

check_workflow_files() {
    echo -e "${BLUE}Workflow Files:${NC}"
    
    if [[ -f ".github/workflows/ci-cd.yml" ]]; then
        print_success "CI/CD workflow file exists"
    else
        print_error "CI/CD workflow file is missing"
    fi
    
    if [[ -f "Dockerfile" ]]; then
        print_success "Dockerfile exists"
    else
        print_error "Dockerfile is missing"
    fi
    
    if [[ -f "scripts/deploy.sh" ]]; then
        print_success "Deployment script exists"
        if [[ -x "scripts/deploy.sh" ]]; then
            print_success "Deployment script is executable"
        else
            print_warning "Deployment script is not executable (run: chmod +x scripts/deploy.sh)"
        fi
    else
        print_error "Deployment script is missing"
    fi
    
    echo
}

main() {
    print_header
    
    # Check workflow files
    check_workflow_files
    
    # Check GitHub CLI
    if ! check_github_cli; then
        show_setup_instructions
        exit 1
    fi
    
    echo
    
    # Check secrets
    if check_secrets; then
        echo
        print_success "Your CI/CD setup is ready!"
        print_info "You can now push to main branch to trigger deployment"
    else
        show_setup_instructions
        exit 1
    fi
}

main "$@"
