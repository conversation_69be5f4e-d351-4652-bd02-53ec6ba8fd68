import { db } from '@/db';
import { chatSessions, chatMessages, users } from '@/db/schema';
import { eq } from 'drizzle-orm';
import ShareChatClient from './share-chat-client';
import { NotPublicPage } from './not-public-page';
import { NotFoundPage } from './not-found-page';
import { getTranslations } from 'next-intl/server';

interface SharePageProps {
  params: {
    sessionId: string;
  };
}

// 获取会话数据并检查公开状态
async function getSessionData(sessionId: string) {
  try {
    // 首先查询会话是否存在
    const session = await db()
      .select({
        id: chatSessions.id,
        uuid: chatSessions.uuid,
        title: chatSessions.title,
        created_at: chatSessions.created_at,
        updated_at: chatSessions.updated_at,
        user_uuid: chatSessions.user_uuid,
        is_public: chatSessions.is_public,
      })
      .from(chatSessions)
      .where(eq(chatSessions.uuid, sessionId))
      .limit(1);

    if (session.length === 0) {
      return { status: 'not_found' as const };
    }

    // 检查会话是否公开
    if (!session[0].is_public) {
      return {
        status: 'not_public' as const,
        session: session[0]
      };
    }

    // 获取用户信息（用于显示分享者）
    const user = await db()
      .select({
        nickname: users.nickname,
        avatar_url: users.avatar_url,
      })
      .from(users)
      .where(eq(users.uuid, session[0].user_uuid))
      .limit(1);

    // 获取会话消息
    const messages = await db()
      .select({
        id: chatMessages.id,
        uuid: chatMessages.uuid,
        role: chatMessages.role,
        content: chatMessages.content,
        message_type: chatMessages.message_type,
        metadata: chatMessages.metadata,
        created_at: chatMessages.created_at,
      })
      .from(chatMessages)
      .where(eq(chatMessages.session_uuid, sessionId))
      .orderBy(chatMessages.created_at);

    return {
      status: 'success' as const,
      session: session[0],
      user: user[0] || null,
      messages: messages.map(msg => ({
        id: msg.id.toString(),
        content: msg.content,
        role: msg.role as 'user' | 'assistant',
        timestamp: new Date(msg.created_at!).getTime(),
        type: msg.message_type as 'message' | 'plan' | 'document' | 'analysis',
      })),
    };
  } catch (error) {
    console.error('Error fetching session:', error);
    return { status: 'error' as const };
  }
}

export default async function SharePage({ params }: SharePageProps) {
  const result = await getSessionData(params.sessionId);

  if (result.status === 'not_found' || result.status === 'error') {
    return <NotFoundPage />;
  }

  if (result.status === 'not_public') {
    // 返回未公开提示页面
    return <NotPublicPage sessionTitle={result.session.title} />;
  }

  return (
    <ShareChatClient
      sessionData={result}
      sessionId={params.sessionId}
    />
  );
}

// 生成页面元数据
export async function generateMetadata({ params }: SharePageProps) {
  const t = await getTranslations('Share.metadata');
  const pageT = await getTranslations('Share.page');
  const result = await getSessionData(params.sessionId);

  if (result.status === 'not_found' || result.status === 'error') {
    return {
      title: t('not_found_title'),
    };
  }

  if (result.status === 'not_public') {
    return {
      title: t('not_public_title', { title: result.session.title }),
      description: t('not_public_description'),
    };
  }

  return {
    title: t('shared_title', { title: result.session.title }),
    description: t('shared_description', { user: result.user?.nickname || pageT('default_user') }),
  };
}