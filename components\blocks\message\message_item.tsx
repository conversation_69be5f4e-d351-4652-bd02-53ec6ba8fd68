import { Button } from "@/components/ui/button";
import { ActionIconGroup, ChatItem, ChatItemProps } from '@ant-design/pro-chat';

interface MessageItemProps {
  content: string;
  onOptimizerShortPlanner?: () => void;
}

const MessageItem = ({ content, onOptimizerShortPlanner }: MessageItemProps) => {
  const control: ChatItemProps | any = {
    loading: false,
    message: content,
    placement: {
      options: ['left', 'right'],
      value: 'left',
    },
    primary: false,
    showTitle: false,
    time: 1_686_538_950_084,
    type: {
      options: ['block', 'pure'],
      value: 'block',
    },
    avatar: {
      title: 'User',
      src: '/imgs/users/1.png', // You can replace this with a real user avatar if available
    },
  };
  return (
    <div className="bg-white dark:bg-zinc-900 rounded-lg shadow p-4 flex flex-col gap-4 border border-zinc-200 dark:border-zinc-800 w-[800px]">
      <ChatItem {...control} />
      <div className="flex gap-2 justify-end">
        <Button variant="outline" size="sm">
        直接生成详细文档
        </Button>
        <Button variant="default" size="sm" onClick={onOptimizerShortPlanner}>
          优化
        </Button>
      </div>
    </div>
  );
};


interface ShortMessageItemProps {
  content: string;
  onGenerateDetailDoc?: (content: string) => void;
}

const ShortMessageItem = ({ content, onGenerateDetailDoc }: ShortMessageItemProps) => {
  const control: ChatItemProps | any = {
    loading: false,
    message: content,
    placement: {
      options: ['left', 'right'],
      value: 'left',
    },
    primary: false,
    showTitle: false,
    time: 1_686_538_950_084,
    type: {
      options: ['block', 'pure'],
      value: 'block',
    },
    avatar: {
      title: 'User',
      src: '/imgs/users/1.png', // You can replace this with a real user avatar if available
    },
  };
  return (
    <div className="bg-white dark:bg-zinc-900 rounded-lg shadow p-4 flex flex-col gap-4 border border-zinc-200 dark:border-zinc-800 w-[800px]">
      <ChatItem {...control} />
      <div className="flex gap-2 justify-end">
        <Button
          variant="default"
          size="sm"
          onClick={() => onGenerateDetailDoc?.(content)}
        >
          直接生成详细文档
        </Button>
      </div>
    </div>
  );
};


export { MessageItem, ShortMessageItem };
