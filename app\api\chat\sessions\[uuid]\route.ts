import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';
import { findChatSessionByUuid, updateChatSession, deleteChatSession } from '@/models/chat';

// PUT /api/chat/sessions/[uuid] - 更新会话
export async function PUT(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;
    const body = await req.json();

    // 验证会话是否属于当前用户
    const existingSession = await findChatSessionByUuid(sessionUuid);
    if (!existingSession || existingSession.user_uuid !== userInfo.uuid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    const updatedSession = await updateChatSession(sessionUuid, body);

    return NextResponse.json({
      success: true,
      data: updatedSession,
    });
  } catch (error) {
    console.error('Failed to update chat session:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/chat/sessions/[uuid] - 删除会话
export async function DELETE(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;

    // 验证会话是否属于当前用户
    const existingSession = await findChatSessionByUuid(sessionUuid);
    if (!existingSession || existingSession.user_uuid !== userInfo.uuid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    await deleteChatSession(sessionUuid);

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully',
    });
  } catch (error) {
    console.error('Failed to delete chat session:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
