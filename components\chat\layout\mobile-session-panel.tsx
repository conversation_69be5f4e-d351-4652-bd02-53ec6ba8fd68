"use client";
import { But<PERSON> } from "@/components/ui/button";
import { X, Home } from "lucide-react";
import SessionList from "@/components/chat/session-list";
import Link from "next/link";

interface MobileSessionPanelProps {
  show: boolean;
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onClose: () => void;
  isNewChatPreparing: boolean;
  refreshTrigger: number;
  t: any;
}

export function MobileSessionPanel({
  show,
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onClose,
  isNewChatPreparing,
  refreshTrigger,
  t
}: MobileSessionPanelProps) {
  if (!show) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
        onClick={onClose}
      />
      {/* 会话列表面板 */}
      <div className="fixed left-0 top-0 bottom-0 z-50 w-80 bg-background border-r shadow-xl transition-transform duration-300 ease-in-out flex flex-col">
        <div className="flex items-center justify-between p-4 border-b flex-shrink-0">
          <h2 className="font-semibold text-lg">{t('session_list.title')}</h2>
          <div className="flex items-center gap-2">
            <Link href="/" onClick={onClose}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                title="返回主页"
              >
                <Home className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex-1 min-h-0">
          <SessionList
            currentSessionId={currentSessionId}
            onSessionSelect={(sessionId) => {
              onSessionSelect(sessionId);
              onClose();
            }}
            onNewSession={() => {
              onNewSession();
              onClose();
            }}
            isNewChatPreparing={isNewChatPreparing}
            className="h-full"
            hideTitleOnMobile={true}
            refreshTrigger={refreshTrigger}
          />
        </div>
      </div>
    </>
  );
}
