/**
 * 复制文本到剪贴板的工具函数
 * 支持现代 Clipboard API 和传统的 execCommand 降级方案
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      return true;
    }
    
    // 降级到传统的复制方法
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
}

/**
 * 显示可选中的文本元素，让用户手动复制
 */
export function showSelectableText(text: string, duration: number = 3000): void {
  try {
    const selection = window.getSelection();
    const range = document.createRange();
    const linkElement = document.createElement('div');
    
    linkElement.textContent = text;
    linkElement.style.position = 'fixed';
    linkElement.style.left = '50%';
    linkElement.style.top = '50%';
    linkElement.style.transform = 'translate(-50%, -50%)';
    linkElement.style.background = 'white';
    linkElement.style.color = 'black';
    linkElement.style.padding = '12px 16px';
    linkElement.style.border = '1px solid #ccc';
    linkElement.style.borderRadius = '8px';
    linkElement.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    linkElement.style.zIndex = '9999';
    linkElement.style.fontSize = '14px';
    linkElement.style.fontFamily = 'monospace';
    linkElement.style.maxWidth = '80vw';
    linkElement.style.wordBreak = 'break-all';
    
    document.body.appendChild(linkElement);
    
    range.selectNodeContents(linkElement);
    selection?.removeAllRanges();
    selection?.addRange(range);
    
    // 指定时间后移除元素
    setTimeout(() => {
      if (document.body.contains(linkElement)) {
        document.body.removeChild(linkElement);
      }
    }, duration);
  } catch (error) {
    console.error('显示可选中文本失败:', error);
  }
}
