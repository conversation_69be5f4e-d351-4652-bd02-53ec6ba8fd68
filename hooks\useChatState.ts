import { useState } from 'react';
import { ChatState } from '@/types/conversation';

// 重新导出Message类型，保持向后兼容
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  type?: 'plan' | 'message' | 'document' | 'analysis';
}

export interface ChatStateHook {
  // 基础状态
  state: ChatState;
  setState: (state: ChatState) => void;
  
  // 用户输入
  userInput: string;
  setUserInput: (input: string) => void;
  
  // 结果状态
  shortPlanResult: string;
  setShortPlanResult: (result: string) => void;
  longDocResult: string;
  setLongDocResult: (result: string) => void;
  
  // 加载状态
  loadingStartTime: number;
  setLoadingStartTime: (time: number) => void;
  
  // 当前需求
  currentRequirement: string;
  setCurrentRequirement: (requirement: string) => void;
  
  // 流式消息状态
  streamingMessage: string;
  setStreamingMessage: (message: string) => void;
  streamingPlan: string;
  setStreamingPlan: (plan: string) => void;
  streamingDoc: string;
  setStreamingDoc: (doc: string) => void;
  streamingAnalysis: string;
  setStreamingAnalysis: (analysis: string) => void;
  
  // 文档面板状态
  showDocPanel: boolean;
  setShowDocPanel: (show: boolean) => void;
  
  // 移动端检测
  isMobile: boolean;
  setIsMobile: (mobile: boolean) => void;
  
  // 消息列表
  messages: Message[];
  setMessages: (messages: Message[] | ((prev: Message[]) => Message[])) => void;
  
  // 编辑状态
  isEditingPlan: boolean;
  setIsEditingPlan: (editing: boolean) => void;
  editablePlan: string;
  setEditablePlan: (plan: string) => void;
  editableDoc: string;
  setEditableDoc: (doc: string) => void;
  
  // 文档查看状态
  isViewingHistoryDoc: boolean;
  setIsViewingHistoryDoc: (viewing: boolean) => void;
  isMarkdownPreview: boolean;
  setIsMarkdownPreview: (preview: boolean) => void;
}

export function useChatState(): ChatStateHook {
  const [state, setState] = useState<ChatState>(ChatState.WELCOME);
  const [userInput, setUserInput] = useState("");
  const [shortPlanResult, setShortPlanResult] = useState("");
  const [longDocResult, setLongDocResult] = useState("");
  const [loadingStartTime, setLoadingStartTime] = useState(0);
  const [currentRequirement, setCurrentRequirement] = useState("");
  const [streamingMessage, setStreamingMessage] = useState("");
  const [streamingPlan, setStreamingPlan] = useState("");
  const [streamingDoc, setStreamingDoc] = useState("");
  const [streamingAnalysis, setStreamingAnalysis] = useState("");
  
  const [showDocPanel, setShowDocPanel] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isEditingPlan, setIsEditingPlan] = useState(false);
  const [editablePlan, setEditablePlan] = useState("");
  const [editableDoc, setEditableDoc] = useState("");
  const [isViewingHistoryDoc, setIsViewingHistoryDoc] = useState(false);
  const [isMarkdownPreview, setIsMarkdownPreview] = useState(false);

  return {
    state,
    setState,
    userInput,
    setUserInput,
    shortPlanResult,
    setShortPlanResult,
    longDocResult,
    setLongDocResult,
    loadingStartTime,
    setLoadingStartTime,
    currentRequirement,
    setCurrentRequirement,
    streamingMessage,
    setStreamingMessage,
    streamingPlan,
    setStreamingPlan,
    streamingDoc,
    setStreamingDoc,
    streamingAnalysis,
    setStreamingAnalysis,
    showDocPanel,
    setShowDocPanel,
    isMobile,
    setIsMobile,
    messages,
    setMessages,
    isEditingPlan,
    setIsEditingPlan,
    editablePlan,
    setEditablePlan,
    editableDoc,
    setEditableDoc,
    isViewingHistoryDoc,
    setIsViewingHistoryDoc,
    isMarkdownPreview,
    setIsMarkdownPreview,
  };
}
