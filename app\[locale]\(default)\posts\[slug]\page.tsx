import { PostStatus, findPostBySlug } from "@/models/post";

import BlogDetail from "@/components/blocks/blog-detail";
import Empty from "@/components/blocks/empty";
import { Post } from "@/types/post";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: { locale: string; slug: string };
}) {
  const t = await getTranslations();

  const post = await findPostBySlug(params.slug, params.locale);

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/posts/${params.slug}`;

  if (params.locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${params.locale}/posts/${params.slug}`;
  }

  return {
    title: post?.title,
    description: post?.description,
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function ({
  params,
}: {
  params: { locale: string; slug: string };
}) {
  const post = await findPostBySlug(params.slug, params.locale);

  if (!post || post.status !== PostStatus.Online) {
    return <Empty message="Post not found" />;
  }

  // Convert database types to Post interface types
  const postData: Post = {
    uuid: post.uuid,
    slug: post.slug || undefined,
    title: post.title || undefined,
    description: post.description || undefined,
    content: post.content || undefined,
    created_at: post.created_at?.toISOString() || undefined,
    updated_at: post.updated_at?.toISOString() || undefined,
    status: post.status || undefined,
    cover_url: post.cover_url || undefined,
    author_name: post.author_name || undefined,
    author_avatar_url: post.author_avatar_url || undefined,
    locale: post.locale || undefined,
  };

  return <BlogDetail post={postData} />;
}
