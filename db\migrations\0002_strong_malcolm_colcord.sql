CREATE TABLE `chat_messages` (
	`id` int AUTO_INCREMENT NOT NULL,
	`uuid` varchar(255) NOT NULL,
	`session_uuid` varchar(255) NOT NULL,
	`user_uuid` varchar(255) NOT NULL,
	`role` varchar(50) NOT NULL,
	`content` text NOT NULL,
	`message_type` varchar(50) DEFAULT 'message',
	`metadata` text,
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `chat_messages_id` PRIMARY KEY(`id`),
	CONSTRAINT `chat_messages_uuid_unique` UNIQUE(`uuid`)
);
--> statement-breakpoint
CREATE TABLE `chat_sessions` (
	`id` int AUTO_INCREMENT NOT NULL,
	`uuid` varchar(255) NOT NULL,
	`user_uuid` varchar(255) NOT NULL,
	`title` varchar(255) NOT NULL,
	`status` varchar(50) NOT NULL DEFAULT 'active',
	`chat_state` varchar(50) DEFAULT 'welcome',
	`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `chat_sessions_id` PRIMARY KEY(`id`),
	CONSTRAINT `chat_sessions_uuid_unique` UNIQUE(`uuid`)
);
