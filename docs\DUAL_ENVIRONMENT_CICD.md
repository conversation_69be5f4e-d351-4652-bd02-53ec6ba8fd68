# 双环境 CI/CD 配置文档

## 概述

本项目现在支持两个独立的部署环境：
- **Production（生产环境）**：用于正式发布
- **Development（开发环境）**：用于开发测试

## 环境配置

### 环境文件
- `.env.production` - 生产环境配置
- `.env.development` - 开发环境配置

### GitHub Secrets 配置

需要在 GitHub 仓库中配置以下 secrets：

#### 通用 Secrets
- `K8S_TOKEN` - Kubernetes API 访问令牌
- `K8S_API_URL` - Kubernetes API 地址
- `NAMESPACE` - Kubernetes 命名空间
- `GHCR_TOKEN` - GitHub Container Registry 访问令牌

#### 环境特定 Secrets
- `DEPLOYMENT_NAME` - 生产环境的 Kubernetes Deployment 名称
- `DEPLOYMENT_DEVELOPMENT_NAME` - 开发环境的 Kubernetes Deployment 名称

## 触发条件

### Production 环境
- **唯一触发方式**：当推送 tag（格式：`v*`）时
- **注意**：Production 环境只能通过推送 tag 触发，无法手动触发

### Development 环境
- **自动触发**：当推送到 `main` 分支时
- **手动触发**：通过 GitHub Actions 手动触发（只能部署到 development 环境）

### Pull Request
- 只运行测试，不进行部署

## 工作流程

### 1. 环境检测
工作流首先根据触发条件自动检测目标环境：
- Tag 推送 → Production
- Main 分支推送 → Development
- 手动触发 → 用户选择的环境

### 2. 测试阶段
- 代码检出
- 依赖安装
- 代码检查（linting）
- 类型检查和构建

### 3. 构建和推送
- 根据环境使用对应的 `.env` 文件构建 Docker 镜像
- 镜像标签包含环境信息，例如：
  - `main-abc1234-development`
  - `v1.0.0-production`

### 4. 部署
- 根据环境使用对应的 Deployment 名称
- 更新 Kubernetes 中的容器镜像
- 验证部署状态

## 使用方法

### 发布到生产环境
```bash
# 创建并推送 tag
git tag v1.0.0
git push origin v1.0.0
```

### 发布到开发环境
```bash
# 推送到 main 分支
git push origin main
```

### 手动触发部署（仅限 Development 环境）
1. 进入 GitHub Actions 页面
2. 选择 "CI/CD Pipeline" 工作流
3. 点击 "Run workflow"
4. 点击 "Run workflow"（将自动部署到 development 环境）

**注意**：手动触发只能部署到 development 环境，production 环境只能通过推送 tag 触发。

## 镜像标签规则

### Development 环境
- 分支推送：`{branch}-{short-sha}-development`
- 例如：`main-abc1234-development`

### Production 环境
- Tag 推送：`{tag}-production`
- 例如：`v1.0.0-production`

## 环境变量差异

主要差异在 Web URL 配置：
- Production: `http://nodeport.sensedeal.vip:32678`
- Development: `http://dev.sensedeal.vip:32679`

其他配置可根据需要在对应的 `.env` 文件中调整。

## 故障排除

### 常见问题
1. **部署失败**：检查 Kubernetes secrets 是否正确配置
2. **镜像构建失败**：检查环境文件是否存在且格式正确
3. **权限错误**：确认 `K8S_TOKEN` 有足够的权限操作目标 Deployment

### 调试步骤
1. 查看 GitHub Actions 日志
2. 检查 Kubernetes 集群状态
3. 验证 secrets 配置
4. 确认镜像是否成功推送到 registry
