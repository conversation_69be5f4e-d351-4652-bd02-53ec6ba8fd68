"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Plus, PanelLeft } from "lucide-react";
import { cn } from "@/lib/utils";

interface CollapsedSessionListProps {
  onNewSession: () => void;
  onExpand: () => void;
  className?: string;
}

export default function CollapsedSessionList({
  onNewSession,
  onExpand,
  className,
}: CollapsedSessionListProps) {
  return (
    <div className={cn(
      "w-16 bg-muted/30 border-r border-border/30 flex flex-col py-4",
      className
    )}>
      {/* 顶部按钮区域 */}
      <div className="flex flex-col items-center space-y-3">
        {/* 展开按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onExpand}
          className="w-10 h-10 p-0 hover:bg-background/50"
          title="展开侧边栏"
        >
          <PanelLeft className="w-4 h-4" />
        </Button>

        {/* 新建对话按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewSession}
          className="w-10 h-10 p-0 hover:bg-background/50"
          title="新建对话"
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>


    </div>
  );
}
