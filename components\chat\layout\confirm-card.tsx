"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Lightbulb, Edit3, Save, FileText, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { StreamingMessage } from "../messages/streaming-message";
import { MarkdownRenderer } from "../ui/markdown-renderer";
import { ChatState } from "@/types/conversation";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  type?: 'plan' | 'message' | 'document' | 'analysis';
}

interface ConfirmCardProps {
  messages: Message[];
  streamingMessage: string;
  isEditingPlan: boolean;
  editablePlan: string;
  onEditablePlanChange: (plan: string) => void;
  onEditPlan: () => void;
  onGenerateDocument: () => void;
  state: ChatState;
  isMobile: boolean;
  locale: string;
  t: any;
  onViewDocument?: (content: string) => void;
}

export function ConfirmCard({
  messages,
  streamingMessage,
  isEditingPlan,
  editablePlan,
  onEditablePlanChange,
  onEditPlan,
  onGenerateDocument,
  state,
  isMobile,
  locale,
  t,
  onViewDocument
}: ConfirmCardProps) {
  // 获取所有规划消息
  const planMessages = messages.filter(message =>
    message.role === 'assistant' && message.type === 'plan'
  );

  // 获取最新的规划消息
  const latestPlanMessage = planMessages[planMessages.length - 1];

  return (
    <div className="max-w-5xl mx-auto space-y-8 px-2">
      {/* 显示完整的对话历史，包括普通对话消息、规划消息和文档消息 */}
      <div className="space-y-6 mb-8">
        {messages
          .filter(message =>
            message.role === 'user' ||
            (message.role === 'assistant' && (message.type === 'message' || message.type === 'plan' || message.type === 'document'))
          )
          .map((message, index) => (
            <div key={message.id} className={cn(
              "flex message-enter",
              message.role === 'user' ? "justify-end" : "justify-start"
            )} style={{ animationDelay: `${index * 0.1}s` }}>
              {message.role === 'user' ? (
                <div className="max-w-[85%] md:max-w-[80%] rounded-2xl px-4 py-3 bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-sm shadow-primary/20">
                  <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                </div>
              ) : message.type === 'message' ? (
                <div className="max-w-[85%] md:max-w-[80%] rounded-2xl px-4 py-3 bg-gradient-to-br from-muted/60 to-muted/40 shadow-sm">
                  <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                </div>
              ) : message.type === 'document' ? (
                <div className="max-w-[85%] md:max-w-[80%] rounded-2xl px-4 py-3 bg-gradient-to-br from-blue-50/80 to-blue-100/60 dark:from-blue-950/40 dark:to-blue-900/30 border border-blue-200/60 dark:border-blue-800/60 shadow-blue-100/50 dark:shadow-blue-900/20">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm font-semibold text-blue-700 dark:text-blue-300">
                        <div className="p-1.5 rounded-lg bg-blue-100 dark:bg-blue-900/50">
                          <FileText className="w-4 h-4" />
                        </div>
                        <span>{t('messages.document_version')}</span>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground bg-white/60 dark:bg-black/20 px-2 py-1 rounded-full">
                        <Clock className="w-3 h-3" />
                        <span>
                          {new Date(message.timestamp).toLocaleString(locale, {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            timeZone: 'UTC' // 显示UTC时间，避免时区转换
                          })}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-3 leading-relaxed bg-white/40 dark:bg-black/10 rounded-lg p-3">
                      {message.content.substring(0, 150)}...
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewDocument?.(message.content)}
                      className="text-xs h-8 rounded-xl bg-white/60 dark:bg-black/20 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all duration-200"
                    >
                      <FileText className="w-3 h-3 mr-1.5" />
                      {t('buttons.view_version')}
                    </Button>
                  </div>
                </div>
              ) : message.type === 'plan' ? (
                <div className="w-full">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-sm">
                      <Lightbulb className="w-5 h-5 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                      {t('messages.plan_title')}
                    </h3>
                  </div>
                  <Card className="bg-gradient-to-br from-muted/40 to-muted/20 border-border/40 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                    <CardContent className="p-6">
                      {/* 只有最新的规划消息才提供编辑功能 */}
                      {message.id === latestPlanMessage?.id ? (
                        <>
                          {isEditingPlan ? (
                            <div className="space-y-4">
                              <div className="bg-background/60 rounded-xl border border-border/30 p-1">
                                <Textarea
                                  value={editablePlan}
                                  onChange={(e) => onEditablePlanChange(e.target.value)}
                                  className="min-h-[200px] resize-none font-mono border-0 bg-transparent focus:ring-0 focus:outline-none"
                                  placeholder={t('placeholders.edit_plan')}
                                />
                              </div>
                            </div>
                          ) : (
                            <div className="bg-background/30 rounded-xl p-4 border border-border/20">
                              <MarkdownRenderer content={message.content} />
                            </div>
                          )}
                          {/* 操作按钮 - 只在文档生成完成前显示 */}
                          {state !== ChatState.LONG_RESULT && (
                            <div className={cn(
                              "pt-6 border-t border-border/20 mt-6",
                              isMobile
                                ? "flex flex-col gap-3"
                                : "flex gap-4 justify-end"
                            )}>
                              <Button
                                variant={isEditingPlan ? "secondary" : "outline"}
                                onClick={onEditPlan}
                                className={cn(
                                  "rounded-xl px-6 py-2.5 font-medium transition-all duration-200 hover:scale-105 active:scale-95",
                                  isMobile ? "w-full" : "",
                                  isEditingPlan ? "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300" : ""
                                )}
                                disabled={state === ChatState.SHORT_CHAT_LOADING || state === ChatState.LONG_LOADING}
                              >
                                {isEditingPlan ? (
                                  <>
                                    <Save className="w-4 h-4 mr-2" />
                                    {t('buttons.save')}
                                  </>
                                ) : (
                                  <>
                                    <Edit3 className="w-4 h-4 mr-2" />
                                    {t('buttons.edit_manually')}
                                  </>
                                )}
                              </Button>
                              <Button
                                onClick={onGenerateDocument}
                                disabled={state === ChatState.LONG_LOADING}
                                className={cn(
                                  "rounded-xl px-6 py-2.5 font-medium transition-all duration-200 hover:scale-105 active:scale-95 bg-gradient-to-r from-primary to-primary/90 shadow-md hover:shadow-lg",
                                  isMobile ? "w-full" : ""
                                )}
                              >
                                <Lightbulb className="w-4 h-4 mr-2" />
                                {t('buttons.generate_document')}
                              </Button>
                            </div>
                          )}
                        </>
                      ) : (
                        // 历史规划消息只显示内容，不提供编辑功能
                        <div className="bg-background/30 rounded-xl p-4 border border-border/20">
                          <MarkdownRenderer content={message.content} />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : null}
            </div>
          ))}

        {/* 显示流式消息 */}
        {streamingMessage && (
          <div className="message-enter">
            <StreamingMessage content={streamingMessage} />
          </div>
        )}
      </div>

      {/* 移除加载指示器，直接显示流式内容 */}

      {/* 如果没有任何规划消息，显示等待提示 */}
      {planMessages.length === 0 && (
        <div className="message-enter">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-sm">
              <Lightbulb className="w-5 h-5 text-primary" />
            </div>
            <h3 className="text-xl font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('messages.plan_title')}
            </h3>
          </div>
          <Card className="bg-gradient-to-br from-muted/40 to-muted/20 border-border/40 shadow-lg overflow-hidden">
            <CardContent className="p-8">
              <div className="text-center py-12 space-y-6">
                <div className="w-16 h-16 rounded-3xl bg-gradient-to-br from-muted/60 to-muted/40 flex items-center justify-center mx-auto shadow-sm">
                  <Lightbulb className="w-8 h-8 text-muted-foreground animate-pulse" />
                </div>
                <div className="space-y-3">
                  <p className="text-xl font-semibold text-muted-foreground">
                    {t('messages.waiting_for_plan')}
                  </p>
                  <p className="text-sm text-muted-foreground/80 max-w-md mx-auto leading-relaxed">
                    {t('messages.continue_conversation')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
