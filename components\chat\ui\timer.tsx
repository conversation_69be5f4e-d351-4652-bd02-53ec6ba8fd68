"use client";
import { useState, useEffect } from "react";
import { Clock } from "lucide-react";

interface TimerProps {
  startTime: number;
  t: any;
}

export function Timer({ startTime, t }: TimerProps) {
  const [elapsed, setElapsed] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsed(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  const minutes = Math.floor(elapsed / 60000);
  const seconds = Math.floor((elapsed % 60000) / 1000);
  const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

  return (
    <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-full">
      <Clock className="w-3 h-3 animate-pulse" />
      <span className="font-mono font-medium">{timeString}</span>
    </div>
  );
}
