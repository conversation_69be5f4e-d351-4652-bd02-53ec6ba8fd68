"use client";
import { useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, HelpCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { ChatState } from "@/types/conversation";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled?: boolean;
  state: ChatState;
  loadingStartTime: number;
  t: any;
  className?: string;
  showHelpTooltip?: boolean;
}

export function ChatInput({
  value,
  onChange,
  onSubmit,
  disabled = false,
  state,
  loadingStartTime,
  t,
  className,
  showHelpTooltip = false
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 动态调整输入框高度
  const adjustTextareaHeight = useCallback(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      // 重置高度以获取正确的 scrollHeight
      textarea.style.height = 'auto';
      // 设置新高度，限制在最小和最大高度之间
      const minHeight = 60; // 最小高度
      const maxHeight = 200; // 最大高度
      const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
      textarea.style.height = `${newHeight}px`;
    }
  }, []);

  // 监听输入内容变化，动态调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [value, adjustTextareaHeight]);

  // 处理表单提交
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  // 处理键盘事件（正确处理中文输入法）
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      // 检查是否在中文输入法的组合状态中
      if (e.nativeEvent.isComposing) {
        return; // 如果在组合状态中，不处理回车
      }
      e.preventDefault();
      onSubmit();
    }
  };

  const getPlaceholder = () => {
    switch (state) {
      case ChatState.WELCOME:
        return t('placeholders.welcome_input');
      case ChatState.SHORT_LOADING:
        return t('placeholders.analyzing');
      case ChatState.SHORT_CHAT_LOADING:
        return t('placeholders.processing');
      case ChatState.SHORT_CONFIRM:
        return t('placeholders.tell_me_changes');
      case ChatState.LONG_LOADING:
        return t('placeholders.generating_document');
      case ChatState.LONG_RESULT:
        return t('placeholders.tell_me_changes');
      default:
        return t('placeholders.continue_chat');
    }
  };

  return (
    <div className={cn("border-t border-border/20 bg-gradient-to-t from-background via-background/98 to-background/95 backdrop-blur-md p-4 shadow-lg", className)}>
      <form onSubmit={handleFormSubmit} className="flex gap-3 items-end w-full max-w-4xl mx-auto">
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            placeholder={getPlaceholder()}
            className={cn(
              "resize-none border-2 text-sm placeholder:text-muted-foreground/70 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/50 rounded-2xl px-4 py-3 pr-12 transition-all duration-200 overflow-y-auto shadow-sm",
              disabled
                ? "bg-muted/20 text-muted-foreground cursor-not-allowed border-border/30"
                : "bg-background/80 hover:bg-background/90 border-border/40 hover:border-border/60 hover:shadow-md"
            )}
            rows={1}
            style={{ minHeight: '60px' }}
          />
          {showHelpTooltip && (
            <div className="absolute top-3 right-3">
              <div className="group relative">
                <div className="p-1 rounded-full bg-muted/50 hover:bg-muted transition-colors">
                  <HelpCircle className="w-4 h-4 text-muted-foreground cursor-help" />
                </div>
                <div className="absolute bottom-8 right-0 hidden group-hover:block bg-popover text-popover-foreground text-xs rounded-xl p-3 shadow-xl border border-border/50 min-w-[240px] max-w-[360px] z-50 backdrop-blur-sm">
                  <div className="font-medium mb-2 text-foreground">
                    {t('tooltips.prompt')}
                  </div>
                  <div className="leading-relaxed text-muted-foreground whitespace-normal break-words">
                    {t('tooltips.update_description')}
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* 移除加载指示器，直接显示流式内容 */}
        </div>
        <Button
          type="submit"
          disabled={!value.trim() || disabled}
          className={cn(
            "rounded-2xl px-5 md:px-7 py-3 text-sm font-medium transition-all duration-200 shrink-0 shadow-md hover:shadow-lg",
            disabled
              ? "opacity-50 cursor-not-allowed"
              : "hover:scale-105 active:scale-95 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
          )}
        >
          <Send className="w-4 h-4" />
        </Button>
      </form>
    </div>
  );
}
