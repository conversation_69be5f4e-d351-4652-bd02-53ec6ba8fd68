import { chatSessions, chatMessages } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and } from "drizzle-orm";
import { getSnowId } from "@/lib/hash";

export enum ChatSessionStatus {
  Active = "active",
  Archived = "archived",
  Deleted = "deleted",
}

export enum ChatState {
  Welcome = "welcome",
  ShortLoading = "short_loading",
  ShortConfirm = "short_confirm",
  LongLoading = "long_loading",
  LongResult = "long_result",
}

export enum MessageRole {
  User = "user",
  Assistant = "assistant",
}

export enum MessageType {
  Message = "message",
  Plan = "plan",
  Document = "document",
  Analysis = "analysis",
}

// Chat Sessions
export async function insertChatSession(
  data: typeof chatSessions.$inferInsert
): Promise<typeof chatSessions.$inferSelect | undefined> {
  const dbData = {
    ...data,
    created_at: data.created_at ? (typeof data.created_at === 'string' ? new Date(data.created_at) : data.created_at) : undefined,
    updated_at: data.updated_at ? (typeof data.updated_at === 'string' ? new Date(data.updated_at) : data.updated_at) : undefined,
  };
  
  await db().insert(chatSessions).values(dbData);
  
  if (data.uuid) {
    return await findChatSessionByUuid(data.uuid);
  }
  
  return undefined;
}

export async function findChatSessionByUuid(
  uuid: string
): Promise<typeof chatSessions.$inferSelect | undefined> {
  const [session] = await db()
    .select()
    .from(chatSessions)
    .where(eq(chatSessions.uuid, uuid))
    .limit(1);

  return session;
}

export async function getChatSessionsByUserUuid(
  userUuid: string,
  page: number = 1,
  limit: number = 50
): Promise<(typeof chatSessions.$inferSelect)[]> {
  const offset = (page - 1) * limit;

  const sessions = await db()
    .select()
    .from(chatSessions)
    .where(and(
      eq(chatSessions.user_uuid, userUuid),
      eq(chatSessions.status, ChatSessionStatus.Active)
    ))
    .orderBy(desc(chatSessions.updated_at))
    .limit(limit)
    .offset(offset);

  return sessions;
}

export async function updateChatSession(
  uuid: string,
  data: Partial<typeof chatSessions.$inferInsert>
): Promise<typeof chatSessions.$inferSelect | undefined> {
  const updateData = {
    ...data,
    updated_at: new Date(),
  };

  await db()
    .update(chatSessions)
    .set(updateData)
    .where(eq(chatSessions.uuid, uuid));

  return await findChatSessionByUuid(uuid);
}

export async function deleteChatSession(uuid: string): Promise<void> {
  await db()
    .update(chatSessions)
    .set({ 
      status: ChatSessionStatus.Deleted,
      updated_at: new Date()
    })
    .where(eq(chatSessions.uuid, uuid));
}

// Chat Messages
export async function insertChatMessage(
  data: typeof chatMessages.$inferInsert
): Promise<typeof chatMessages.$inferSelect | undefined> {
  const dbData = {
    ...data,
    created_at: data.created_at ? (typeof data.created_at === 'string' ? new Date(data.created_at) : data.created_at) : undefined,
  };
  
  await db().insert(chatMessages).values(dbData);
  
  if (data.uuid) {
    return await findChatMessageByUuid(data.uuid);
  }
  
  return undefined;
}

export async function findChatMessageByUuid(
  uuid: string
): Promise<typeof chatMessages.$inferSelect | undefined> {
  const [message] = await db()
    .select()
    .from(chatMessages)
    .where(eq(chatMessages.uuid, uuid))
    .limit(1);

  return message;
}

export async function getChatMessagesBySessionUuid(
  sessionUuid: string,
  page: number = 1,
  limit: number = 100
): Promise<(typeof chatMessages.$inferSelect)[]> {
  const offset = (page - 1) * limit;

  const messages = await db()
    .select()
    .from(chatMessages)
    .where(eq(chatMessages.session_uuid, sessionUuid))
    .orderBy(chatMessages.created_at)
    .limit(limit)
    .offset(offset);

  return messages;
}

export async function createNewChatSession(
  userUuid: string,
  title?: string
): Promise<typeof chatSessions.$inferSelect | undefined> {
  const sessionUuid = getSnowId();
  const defaultTitle = title || `会话 ${new Date().toLocaleString('zh-CN')}`;

  return await insertChatSession({
    uuid: sessionUuid,
    user_uuid: userUuid,
    title: defaultTitle,
    status: ChatSessionStatus.Active,
    chat_state: ChatState.Welcome,
  });
}

export async function addMessageToSession(
  sessionUuid: string,
  userUuid: string,
  role: MessageRole,
  content: string,
  messageType: MessageType = MessageType.Message,
  metadata?: any
): Promise<typeof chatMessages.$inferSelect | undefined> {
  const messageUuid = getSnowId();

  // Update session's updated_at timestamp
  await updateChatSession(sessionUuid, {});

  return await insertChatMessage({
    uuid: messageUuid,
    session_uuid: sessionUuid,
    user_uuid: userUuid,
    role,
    content,
    message_type: messageType,
    metadata: metadata ? JSON.stringify(metadata) : null,
  });
}

export async function updateChatMessage(
  messageUuid: string,
  data: Partial<typeof chatMessages.$inferInsert>
): Promise<typeof chatMessages.$inferSelect | undefined> {
  await db()
    .update(chatMessages)
    .set(data)
    .where(eq(chatMessages.uuid, messageUuid));

  return await findChatMessageByUuid(messageUuid);
}
