import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";
import { Order } from "@/types/order";

import <PERSON><PERSON> from "stripe";
import { updateAffiliateForOrder } from "./affiliate";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paied order
        // Convert database result to Order type
        const orderForCredit: Order = {
          order_no: order.order_no,
          created_at: order.created_at ? order.created_at.toISOString() : "",
          user_uuid: order.user_uuid || "",
          user_email: order.user_email || "",
          amount: order.amount,
          interval: order.interval || "",
          expired_at: order.expired_at ? order.expired_at.toISOString() : "",
          status: order.status || "",
          stripe_session_id: order.stripe_session_id || undefined,
          credits: order.credits,
          currency: order.currency || "",
          sub_id: order.sub_id || undefined,
          sub_interval_count: order.sub_interval_count || undefined,
          sub_cycle_anchor: order.sub_cycle_anchor || undefined,
          sub_period_end: order.sub_period_end || undefined,
          sub_period_start: order.sub_period_start || undefined,
          sub_times: order.sub_times || undefined,
          product_id: order.product_id || undefined,
          product_name: order.product_name || undefined,
          valid_months: order.valid_months || undefined,
          order_detail: order.order_detail || undefined,
          paid_at: order.paid_at ? order.paid_at.toISOString() : undefined,
          paid_email: order.paid_email || undefined,
          paid_detail: order.paid_detail || undefined,
        };
        await updateCreditForOrder(orderForCredit);
      }

      // update affiliate for paied order
      // Convert database result to Order type
      const orderForAffiliate: Order = {
        order_no: order.order_no,
        created_at: order.created_at ? order.created_at.toISOString() : "",
        user_uuid: order.user_uuid || "",
        user_email: order.user_email || "",
        amount: order.amount,
        interval: order.interval || "",
        expired_at: order.expired_at ? order.expired_at.toISOString() : "",
        status: order.status || "",
        stripe_session_id: order.stripe_session_id || undefined,
        credits: order.credits,
        currency: order.currency || "",
        sub_id: order.sub_id || undefined,
        sub_interval_count: order.sub_interval_count || undefined,
        sub_cycle_anchor: order.sub_cycle_anchor || undefined,
        sub_period_end: order.sub_period_end || undefined,
        sub_period_start: order.sub_period_start || undefined,
        sub_times: order.sub_times || undefined,
        product_id: order.product_id || undefined,
        product_name: order.product_name || undefined,
        valid_months: order.valid_months || undefined,
        order_detail: order.order_detail || undefined,
        paid_at: order.paid_at ? order.paid_at.toISOString() : undefined,
        paid_email: order.paid_email || undefined,
        paid_detail: order.paid_detail || undefined,
      };
      await updateAffiliateForOrder(orderForAffiliate);
    }

    console.log(
      "handle order session successed: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}
