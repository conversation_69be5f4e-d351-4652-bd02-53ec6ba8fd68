# GTPlanner CI/CD Setup Guide

本文档详细说明了如何为GTPlanner项目设置GitHub CI/CD流程。

## 概述

CI/CD流程包括以下步骤：
1. **测试阶段**: 代码检查、类型检查和构建验证
2. **构建阶段**: Docker镜像构建并推送到GitHub Container Registry
3. **部署阶段**: 自动部署到Kubernetes集群

## 必需的GitHub Secrets配置

在GitHub仓库的Settings > Secrets and variables > Actions中配置以下secrets：

### Kubernetes相关配置

| Secret名称 | 描述 | 示例值 |
|-----------|------|--------|
| `K8S_TOKEN` | Kubernetes API访问令牌 | `eyJhbGciOiJSUzI1NiIsImtpZCI6...` |
| `K8S_API_URL` | Kubernetes API服务器地址 | `http://kuboard.sensedeal.wiki/k8s-api` |
| `NAMESPACE` | 部署的命名空间 | `cubechat` |
| `DEPLOYMENT_NAME` | 部署名称 | `gtplanner-frontend` |

### 应用配置（可选）

如果需要在构建时注入环境变量，可以配置以下secrets：

| Secret名称 | 描述 | 是否必需 |
|-----------|------|---------|
| `DATABASE_URL` | 数据库连接字符串 | 否 |
| `AUTH_SECRET` | NextAuth密钥 | 否 |
| `OPENAI_API_KEY` | OpenAI API密钥 | 否 |

## 环境变量配置

### GitHub Actions环境变量

以下环境变量在workflow中自动设置：

- `REGISTRY`: `ghcr.io` (GitHub Container Registry)
- `IMAGE_NAME`: `${{ github.repository }}` (自动获取仓库名)
- `CI_REGISTRY_IMAGE`: `ghcr.io/${{ github.repository }}`
- `VERSION`: `${{ github.sha }}` (Git提交SHA)

### 运行时环境变量

在Kubernetes部署中，确保以下环境变量已正确配置：

```yaml
env:
  - name: DATABASE_URL
    valueFrom:
      secretKeyRef:
        name: gtplanner-secrets
        key: database-url
  - name: AUTH_SECRET
    valueFrom:
      secretKeyRef:
        name: gtplanner-secrets
        key: auth-secret
  - name: NEXT_PUBLIC_WEB_URL
    value: "https://your-domain.com"
```

## 获取Kubernetes Token

### 方法1: 使用ServiceAccount (推荐)

1. 创建ServiceAccount:
```bash
kubectl create serviceaccount gtplanner-deployer -n cubechat
```

2. 创建ClusterRoleBinding:
```bash
kubectl create clusterrolebinding gtplanner-deployer \
  --clusterrole=edit \
  --serviceaccount=cubechat:gtplanner-deployer
```

3. 获取Token:
```bash
kubectl create token gtplanner-deployer -n cubechat --duration=8760h
```

### 方法2: 使用现有Token

如果您已经有Kuboard的访问token，可以直接使用。

## 部署流程说明

### 触发条件

- **自动触发**: 推送到`main`或`develop`分支
- **手动触发**: 在GitHub Actions页面手动运行workflow

### 部署步骤

1. **代码检查**: ESLint检查和TypeScript类型检查
2. **构建测试**: 验证应用可以正常构建
3. **Docker构建**: 多阶段构建优化的Docker镜像
4. **镜像推送**: 推送到GitHub Container Registry
5. **K8s部署**: 更新Kubernetes deployment
6. **部署验证**: 检查部署状态确保成功

### 镜像标签策略

- `main`分支: `main-{short-sha}` 和 `latest`
- `develop`分支: `develop-{short-sha}`
- Pull Request: `pr-{pr-number}`

## 故障排除

### 常见问题

1. **权限错误**
   - 检查K8S_TOKEN是否有效
   - 确认ServiceAccount有足够权限

2. **镜像拉取失败**
   - 确认GitHub Container Registry权限
   - 检查镜像标签是否正确

3. **部署超时**
   - 检查Kubernetes集群状态
   - 查看Pod日志排查问题

4. **ESLint错误**
   - 项目已配置ESLint，允许最多50个警告
   - 如需修改规则，编辑`.eslintrc.json`文件
   - 运行`pnpm lint`本地检查代码质量

### 调试命令

```bash
# 检查deployment状态
kubectl get deployment gtplanner-frontend -n cubechat

# 查看Pod日志
kubectl logs -f deployment/gtplanner-frontend -n cubechat

# 查看事件
kubectl get events -n cubechat --sort-by='.lastTimestamp'
```

## 本地测试

### 构建Docker镜像

```bash
docker build -t gtplanner-frontend:local .
```

### 运行容器

```bash
# 运行应用
docker run -p 3000:3000 gtplanner-frontend:local

# 运行数据库迁移
docker run --rm gtplanner-frontend:local migrate
```

## 安全注意事项

1. **Secrets管理**: 不要在代码中硬编码敏感信息
2. **Token权限**: 使用最小权限原则配置ServiceAccount
3. **镜像安全**: 定期更新基础镜像和依赖
4. **网络安全**: 确保Kubernetes API访问受到适当保护

## 监控和日志

建议配置以下监控：

1. **应用监控**: 使用Prometheus + Grafana
2. **日志聚合**: 使用ELK Stack或类似工具
3. **告警**: 配置部署失败和应用异常告警

## 更新和维护

1. **定期更新**: 保持依赖和基础镜像最新
2. **备份策略**: 确保数据库和配置有适当备份
3. **回滚计划**: 准备快速回滚机制

---

如有问题，请查看GitHub Actions日志或联系运维团队。
