#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that all hardcoded URLs have been properly refactored to use environment variables
 * Usage: node scripts/verify-url-refactoring.js
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// List of hardcoded URLs that should no longer exist in the codebase
const HARDCODED_URLS_TO_CHECK = [
  'https://github.com/TABai/TAB-template-one',
  'https://github.com/gtplanner',
  'https://discord.gg/HQNnrzjZQS',
  'https://x.com/TABai',
  'mailto:<EMAIL>',
  'https://ui.shadcn.com/themes',
  'https://cli.github.com/',
  'https://vibecoding.sop.best',
  'https://docs.vibecoding.sop.best',
  'https://TAB.ai',
  'https://docs.TAB.ai'
];

// Files to exclude from checking (these are allowed to have hardcoded URLs)
const EXCLUDED_FILES = [
  'lib/config/urls.ts', // Contains fallback URLs
  '.env',
  '.env.example',
  'scripts/verify-url-refactoring.js', // This file
  'README.md', // May contain example URLs
  'LICENSE', // License file can contain URLs
  'package.json', // Package.json homepage can remain hardcoded
  'scripts/validate-secrets.sh' // Script with fallback URLs
];

// Directories to exclude
const EXCLUDED_DIRS = [
  'node_modules',
  '.next',
  '.git',
  'dist',
  'build'
];

/**
 * Recursively get all files in a directory
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const relativePath = path.relative(process.cwd(), fullPath);
    
    if (fs.statSync(fullPath).isDirectory()) {
      if (!EXCLUDED_DIRS.some(dir => relativePath.includes(dir))) {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
      }
    } else {
      if (!EXCLUDED_FILES.some(excludedFile => relativePath.endsWith(excludedFile))) {
        arrayOfFiles.push(fullPath);
      }
    }
  });

  return arrayOfFiles;
}

/**
 * Check if a file contains any hardcoded URLs
 */
function checkFileForHardcodedUrls(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const foundUrls = [];

    HARDCODED_URLS_TO_CHECK.forEach(url => {
      if (content.includes(url)) {
        foundUrls.push(url);
      }
    });

    return foundUrls;
  } catch (error) {
    // Skip files that can't be read (binary files, etc.)
    return [];
  }
}

/**
 * Verify environment variables are properly set
 */
function verifyEnvironmentVariables() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_GITHUB_URL',
    'NEXT_PUBLIC_GITHUB_TEMPLATE_URL',
    'NEXT_PUBLIC_DISCORD_URL',
    'NEXT_PUBLIC_TWITTER_URL',
    'NEXT_PUBLIC_SUPPORT_EMAIL',
    'NEXT_PUBLIC_SHADCN_THEMES_URL',
    'NEXT_PUBLIC_GITHUB_CLI_URL',
    'NEXT_PUBLIC_SITE_URL',
    'NEXT_PUBLIC_HOMEPAGE_URL'
  ];

  const missingVars = [];
  const setVars = [];

  requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
      setVars.push(`✅ ${varName}: ${process.env[varName]}`);
    } else {
      missingVars.push(`❌ ${varName}: Not set`);
    }
  });

  return { missingVars, setVars };
}

/**
 * Main verification function
 */
function main() {
  console.log('🔍 Verifying URL refactoring...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  const { missingVars, setVars } = verifyEnvironmentVariables();
  
  setVars.forEach(msg => console.log(msg));
  missingVars.forEach(msg => console.log(msg));
  
  if (missingVars.length > 0) {
    console.log(`\n⚠️  ${missingVars.length} environment variable(s) are missing`);
  } else {
    console.log('\n✅ All required environment variables are set');
  }

  // Check for hardcoded URLs in files
  console.log('\n🔍 Scanning files for hardcoded URLs...');
  const allFiles = getAllFiles(process.cwd());
  const filesWithHardcodedUrls = [];

  allFiles.forEach(filePath => {
    const foundUrls = checkFileForHardcodedUrls(filePath);
    if (foundUrls.length > 0) {
      filesWithHardcodedUrls.push({
        file: path.relative(process.cwd(), filePath),
        urls: foundUrls
      });
    }
  });

  if (filesWithHardcodedUrls.length === 0) {
    console.log('✅ No hardcoded URLs found in scanned files');
  } else {
    console.log(`\n❌ Found hardcoded URLs in ${filesWithHardcodedUrls.length} file(s):`);
    filesWithHardcodedUrls.forEach(({ file, urls }) => {
      console.log(`\n📄 ${file}:`);
      urls.forEach(url => console.log(`   - ${url}`));
    });
  }

  // Summary
  console.log('\n📊 Summary:');
  console.log(`- Environment variables missing: ${missingVars.length}`);
  console.log(`- Files with hardcoded URLs: ${filesWithHardcodedUrls.length}`);
  console.log(`- Total files scanned: ${allFiles.length}`);

  if (missingVars.length === 0 && filesWithHardcodedUrls.length === 0) {
    console.log('\n🎉 URL refactoring verification completed successfully!');
    process.exit(0);
  } else {
    console.log('\n❌ URL refactoring verification failed. Please address the issues above.');
    process.exit(1);
  }
}

main();
