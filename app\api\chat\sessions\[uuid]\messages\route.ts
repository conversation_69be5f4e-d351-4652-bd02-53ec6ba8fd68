import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';
import { 
  findChatSessionByUuid, 
  getChatMessagesBySessionUuid, 
  addMessageToSession,
  MessageRole,
  MessageType 
} from '@/models/chat';

// GET /api/chat/sessions/[uuid]/messages - 获取会话的消息列表
export async function GET(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '100');

    // 验证会话是否属于当前用户
    const session = await findChatSessionByUuid(sessionUuid);
    if (!session || session.user_uuid !== userInfo.uuid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    const messages = await getChatMessagesBySessionUuid(sessionUuid, page, limit);

    return NextResponse.json({
      success: true,
      data: messages,
    });
  } catch (error) {
    console.error('Failed to get chat messages:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/chat/sessions/[uuid]/messages - 添加新消息到会话
export async function POST(
  req: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionUuid = params.uuid;
    const body = await req.json();
    const { role, content, message_type, metadata } = body;

    // 验证会话是否属于当前用户
    const session = await findChatSessionByUuid(sessionUuid);
    if (!session || session.user_uuid !== userInfo.uuid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // 验证必需字段
    if (!role || !content) {
      return NextResponse.json({ error: 'Role and content are required' }, { status: 400 });
    }

    const message = await addMessageToSession(
      sessionUuid,
      userInfo.uuid,
      role as MessageRole,
      content,
      message_type as MessageType || MessageType.Message,
      metadata
    );

    if (!message) {
      return NextResponse.json({ error: 'Failed to create message' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: message,
    });
  } catch (error) {
    console.error('Failed to create chat message:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
