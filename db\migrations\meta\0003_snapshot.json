{"version": "5", "dialect": "mysql", "id": "0fe185a5-ca50-41f2-a22a-c8ab009ae9a4", "prevId": "e46909f9-f2a1-4d53-b24a-8681e6682bb5", "tables": {"affiliates": {"name": "affiliates", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "paid_order_no": {"name": "paid_order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "paid_amount": {"name": "paid_amount", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "reward_percent": {"name": "reward_percent", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "reward_amount": {"name": "reward_amount", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"affiliates_id": {"name": "affiliates_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "apikeys": {"name": "apikeys", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "api_key": {"name": "api_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"apikeys_id": {"name": "apikeys_id", "columns": ["id"]}}, "uniqueConstraints": {"apikeys_api_key_unique": {"name": "apikeys_api_key_unique", "columns": ["api_key"]}}, "checkConstraint": {}}, "chat_messages": {"name": "chat_messages", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "session_uuid": {"name": "session_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message_type": {"name": "message_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'message'"}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"chat_messages_id": {"name": "chat_messages_id", "columns": ["id"]}}, "uniqueConstraints": {"chat_messages_uuid_unique": {"name": "chat_messages_uuid_unique", "columns": ["uuid"]}}, "checkConstraint": {}}, "chat_sessions": {"name": "chat_sessions", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'active'"}, "chat_state": {"name": "chat_state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'welcome'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"chat_sessions_id": {"name": "chat_sessions_id", "columns": ["id"]}}, "uniqueConstraints": {"chat_sessions_uuid_unique": {"name": "chat_sessions_uuid_unique", "columns": ["uuid"]}}, "checkConstraint": {}}, "credits": {"name": "credits", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "trans_no": {"name": "trans_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "trans_type": {"name": "trans_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "credits": {"name": "credits", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "expired_at": {"name": "expired_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"credits_id": {"name": "credits_id", "columns": ["id"]}}, "uniqueConstraints": {"credits_trans_no_unique": {"name": "credits_trans_no_unique", "columns": ["trans_no"]}}, "checkConstraint": {}}, "feedbacks": {"name": "feedbacks", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "rating": {"name": "rating", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"feedbacks_id": {"name": "feedbacks_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "orders": {"name": "orders", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "amount": {"name": "amount", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "expired_at": {"name": "expired_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "stripe_session_id": {"name": "stripe_session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits": {"name": "credits", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_id": {"name": "sub_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_interval_count": {"name": "sub_interval_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_cycle_anchor": {"name": "sub_cycle_anchor", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_period_end": {"name": "sub_period_end", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_period_start": {"name": "sub_period_start", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_times": {"name": "sub_times", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_name": {"name": "product_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "valid_months": {"name": "valid_months", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_detail": {"name": "order_detail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "paid_email": {"name": "paid_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "paid_detail": {"name": "paid_detail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"orders_id": {"name": "orders_id", "columns": ["id"]}}, "uniqueConstraints": {"orders_order_no_unique": {"name": "orders_order_no_unique", "columns": ["order_no"]}}, "checkConstraint": {}}, "posts": {"name": "posts", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_url": {"name": "cover_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "author_name": {"name": "author_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "author_avatar_url": {"name": "author_avatar_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"posts_id": {"name": "posts_id", "columns": ["id"]}}, "uniqueConstraints": {"posts_uuid_unique": {"name": "posts_uuid_unique", "columns": ["uuid"]}}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "signin_type": {"name": "signin_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "signin_ip": {"name": "signin_ip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "signin_provider": {"name": "signin_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "signin_openid": {"name": "signin_openid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "invite_code": {"name": "invite_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "is_affiliate": {"name": "is_affiliate", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "api_key": {"name": "api_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}}, "indexes": {"email_provider_unique_idx": {"name": "email_provider_unique_idx", "columns": ["email", "signin_provider"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_uuid_unique": {"name": "users_uuid_unique", "columns": ["uuid"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}