import "@/app/globals.css";

import { getMessages } from "next-intl/server";
import { Inter as FontSans } from "next/font/google";
import { Metada<PERSON> } from "next";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { cn } from "@/lib/utils";
import { Toaster } from "@/components/ui/sonner";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export default async function ShareLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // 使用默认语言获取消息
  const messages = await getMessages({ locale: 'zh' });

  return (
    <html lang="zh" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased overflow-x-hidden",
          fontSans.variable
        )}
      >
        <NextIntlClientProvider messages={messages}>
          <NextThemesProvider attribute="class" disableTransitionOnChange>
            {children}
            <Toaster />
          </NextThemesProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
