/**
 * Centralized URL configuration
 * All external URLs and links are managed here using environment variables
 * Compatible with Next.js 14 App Router - uses NEXT_PUBLIC_ prefixed variables
 */

// Helper function to get environment variable with fallback
const getEnvVar = (key: string, fallback: string): string => {
  if (typeof window !== 'undefined') {
    // Client-side: only NEXT_PUBLIC_ variables are available
    return (window as any).__NEXT_DATA__?.env?.[key] || process.env[key] || fallback;
  }
  // Server-side: all environment variables are available
  return process.env[key] || fallback;
};

export const urls = {
  // Social Media URLs
  social: {
    github: getEnvVar('NEXT_PUBLIC_GITHUB_URL', "https://github.com/gtplanner"),
    githubTemplate: getEnvVar('NEXT_PUBLIC_GITHUB_TEMPLATE_URL', "https://github.com/TABai/TAB-template-one"),
    discord: getEnvVar('NEXT_PUBLIC_DISCORD_URL', "https://discord.gg/HQNnrzjZQS"),
    twitter: getEnvVar('NEXT_PUBLIC_TWITTER_URL', "https://x.com/TABai"),
  },

  // Contact Information
  contact: {
    supportEmail: getEnvVar('NEXT_PUBLIC_SUPPORT_EMAIL', "<EMAIL>"),
    get supportEmailUrl() {
      return `mailto:${this.supportEmail}`;
    },
  },

  // Documentation URLs
  docs: {
    shadcnThemes: getEnvVar('NEXT_PUBLIC_SHADCN_THEMES_URL', "https://ui.shadcn.com/themes"),
    githubCli: getEnvVar('NEXT_PUBLIC_GITHUB_CLI_URL', "https://cli.github.com/"),
    tabAi: getEnvVar('NEXT_PUBLIC_TAB_AI_DOCS_URL', "https://docs.TAB.ai"),
  },

  // Site URLs
  site: {
    url: getEnvVar('NEXT_PUBLIC_SITE_URL', "https://vibecoding.sop.best"),
    homepage: getEnvVar('NEXT_PUBLIC_HOMEPAGE_URL', "https://vibecoding.sop.best"),
    webUrl: getEnvVar('NEXT_PUBLIC_WEB_URL', "http://localhost:3000"),
    tabAi: getEnvVar('NEXT_PUBLIC_TAB_AI_URL', "https://TAB.ai"),
  },

  // Payment URLs (already using env vars but centralizing here)
  payment: {
    successUrl: getEnvVar('NEXT_PUBLIC_PAY_SUCCESS_URL', "http://localhost:3000/my-orders"),
    failUrl: getEnvVar('NEXT_PUBLIC_PAY_FAIL_URL', "http://localhost:3000/#pricing"),
    cancelUrl: getEnvVar('NEXT_PUBLIC_PAY_CANCEL_URL', "http://localhost:3000/#pricing"),
  },
} as const;

/**
 * Helper function to get social media URLs for components
 */
export const getSocialLinks = () => [
  {
    title: "Github",
    icon: "RiGithubFill",
    url: urls.social.github,
    target: "_blank",
  },
  {
    title: "Email",
    icon: "RiMailLine", 
    url: urls.contact.supportEmailUrl,
    target: "_self",
  },
];

/**
 * Helper function to get admin social links
 */
export const getAdminSocialLinks = () => [
  {
    title: "Home",
    url: "/",
    target: "_blank",
    icon: "RiHomeLine",
  },
  {
    title: "Github",
    url: urls.social.githubTemplate,
    target: "_blank",
    icon: "RiGithubLine",
  },
  {
    title: "Discord",
    url: urls.social.discord,
    target: "_blank",
    icon: "RiDiscordLine",
  },
  {
    title: "X",
    url: urls.social.twitter,
    target: "_blank",
    icon: "RiTwitterLine",
  },
];

export default urls;
