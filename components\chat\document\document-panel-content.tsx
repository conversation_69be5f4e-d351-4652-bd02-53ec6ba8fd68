"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { MarkdownRenderer } from "../ui/markdown-renderer";
import { ChatState } from "@/types/conversation";

interface DocumentPanelContentProps {
  content: string;
  onChange: (content: string) => void;
  isMarkdownPreview: boolean;
  isViewingHistory: boolean;
  onBackToProgress: () => void;
  state: ChatState;
  t: any;
  readOnly?: boolean;
}

export function DocumentPanelContent({
  content,
  onChange,
  isMarkdownPreview,
  isViewingHistory,
  onBackToProgress,
  state,
  t,
  readOnly = false
}: DocumentPanelContentProps) {
  // 判断是否应该跳过Mermaid渲染（流式输出阶段）
  const shouldSkipMermaid = state === ChatState.LONG_LOADING && !isViewingHistory;

  return (
    <div className="h-full flex flex-col min-h-0 overflow-hidden rounded-2xl">
      {isViewingHistory && state === ChatState.LONG_LOADING && (
        <div className="flex-shrink-0 m-4 mb-0 p-3 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
              <span>📄</span>
              <span>{t('messages.viewing_history')}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onBackToProgress}
              className="text-xs h-7"
            >
              {t('buttons.back_to_progress')}
            </Button>
          </div>
        </div>
      )}
      {isMarkdownPreview ? (
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full overflow-auto p-4">
            <MarkdownRenderer content={content} skipMermaid={shouldSkipMermaid} />
          </div>
        </div>
      ) : (
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full flex flex-col p-4">
            <Textarea
              value={content}
              onChange={(e) => onChange(e.target.value)}
              className="flex-1 w-full resize-none border-0 bg-transparent text-sm focus:outline-none font-mono leading-relaxed min-h-0"
              placeholder={t('placeholders.document_content')}
              readOnly={readOnly}
            />
          </div>
        </div>
      )}
    </div>
  );
}
