"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { X, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { useTranslations } from "next-intl";

interface MarkdownRendererProps {
  content: string;
  skipMermaid?: boolean; // 是否跳过mermaid渲染（用于流式输出）
}

export function MarkdownRenderer({ content, skipMermaid = false }: MarkdownRendererProps) {
  const t = useTranslations('chat');
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [fullscreenMermaid, setFullscreenMermaid] = useState<{ svg: string; code: string } | null>(null);
  const [mermaidData, setMermaidData] = useState<{ [key: string]: { svg: string; code: string } }>({});

  // 缩放和平移状态
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 移动端检测
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const renderMarkdown = async () => {
      if (!content) {
        setRenderedContent('');
        return;
      }

      try {
        // 动态导入 markdown-it 和 mermaid
        const MarkdownIt = (await import('markdown-it')).default;
        const mermaid = (await import('mermaid')).default;

        // 初始化 mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit',
        });

        // 创建 markdown-it 实例
        const md = new MarkdownIt({
          html: true,
          linkify: true,
          typographer: true,
          breaks: true,
        });

        // 预处理 mermaid 代码块
        let processedContent = content;
        const mermaidBlocks: { id: string; svg: string }[] = [];

        if (skipMermaid) {
          // 如果跳过mermaid渲染（流式输出时），保持原始代码块格式
          // 不做任何替换，让markdown-it正常渲染为代码块
        } else {
          // 使用更安全的方法：先分割内容，然后逐个处理mermaid代码块
          const parts = content.split(/```mermaid\s*\n/);
          let blockIndex = 0;
          const newMermaidData: { [key: string]: { svg: string; code: string } } = {};

          // 如果没有mermaid代码块，直接跳过
          if (parts.length === 1) {
            // 没有mermaid代码块
          } else {
            // 重新构建内容
            processedContent = parts[0]; // 第一部分（mermaid之前的内容）

            for (let i = 1; i < parts.length; i++) {
              const part = parts[i];
              // 查找这个部分中的结束标记
              const endIndex = part.indexOf('\n```');

              if (endIndex === -1) {
                // 没有找到结束标记，可能是不完整的代码块，当作普通文本处理
                console.warn('Incomplete mermaid code block found, treating as plain text');
                processedContent += '```mermaid\n' + part;
                continue;
              }

              const mermaidCode = part.substring(0, endIndex).trim();
              const remainingContent = part.substring(endIndex + 4); // +4 for '\n```'
              const id = `mermaid-${blockIndex++}`;

              // 验证mermaid代码是否为空
              if (!mermaidCode) {
                console.warn('Empty mermaid code block found, skipping');
                processedContent += '```mermaid\n\n```' + remainingContent;
                continue;
              }

              try {
                // 渲染 mermaid 图表
                const { svg } = await mermaid.render(id, mermaidCode);
                mermaidBlocks.push({ id, svg });

                // 存储mermaid数据
                newMermaidData[id] = { svg, code: mermaidCode };

                // 添加渲染后的mermaid容器
                processedContent += `<div class="mermaid-container cursor-pointer hover:shadow-lg transition-shadow duration-200 relative group border rounded-lg p-4" data-mermaid-id="${id}">
                  ${svg}
                  <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 text-white px-2 py-1 rounded text-xs">
                    ${t('mermaid.click_to_fullscreen')}
                  </div>
                </div>`;
              } catch (error) {
                console.error('Mermaid rendering error:', error);

                // 尝试自动修复常见的语法问题
                let fixedCode = mermaidCode;
                let renderSuccess = false;

                try {
                  // 修复1: 为包含中文的节点标签添加引号
                  fixedCode = mermaidCode.replace(/(\w+)\[([^\]]*[\u4e00-\u9fa5][^\]]*)\]/g, '$1["$2"]');

                  // 修复2: 为包含特殊字符的节点标签添加引号
                  fixedCode = fixedCode.replace(/(\w+)\[([^\]]*[^\w\s][^\]]*)\]/g, '$1["$2"]');

                  // 修复3: 处理箭头后面直接跟中文节点的情况
                  fixedCode = fixedCode.replace(/(->|--)\s*(\w+)\[([^\]]*[\u4e00-\u9fa5][^\]]*)\]/g, '$1 $2["$3"]');

                  // 修复4: 处理箭头后面直接跟特殊字符节点的情况
                  fixedCode = fixedCode.replace(/(->|--)\s*(\w+)\[([^\]]*[^\w\s][^\]]*)\]/g, '$1 $2["$3"]');

                  // 修复5: 处理带引号的文本中的引号转义问题
                  fixedCode = fixedCode.replace(/\[([^\]]*)"([^\]]*)\]/g, '["$1\\"$2"]');

                  // 修复6: 处理节点中包含中文但没有使用方括号的情况
                  fixedCode = fixedCode.replace(/(\w+)([\u4e00-\u9fa5]+)/g, '$1["$2"]');

                  console.log('Attempting to fix mermaid code:', fixedCode);
                  const { svg: fixedSvg } = await mermaid.render(`${id}-fixed`, fixedCode);

                  // 修复成功，使用修复后的代码
                  mermaidBlocks.push({ id, svg: fixedSvg });
                  newMermaidData[id] = { svg: fixedSvg, code: fixedCode };

                  processedContent += `<div class="mermaid-container cursor-pointer hover:shadow-lg transition-shadow duration-200 relative group border rounded-lg p-4" data-mermaid-id="${id}">
                    ${fixedSvg}
                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 text-white px-2 py-1 rounded text-xs">
                      ${t('mermaid.click_to_fullscreen')}
                    </div>
                  </div>`;
                  renderSuccess = true;
                } catch (fixError) {
                  console.error('Failed to fix mermaid code:', fixError);
                }

                // 如果修复失败，显示错误提示
                if (!renderSuccess) {
                  processedContent += `<div class="mermaid-error border border-amber-200 rounded-lg p-4 bg-amber-50 dark:bg-amber-900/20 mb-4">
                    <div class="flex items-center gap-2 mb-2">
                      <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-amber-800 dark:text-amber-200 font-medium text-sm">${t('mermaid.rendering_error')}</span>
                    </div>
                    <details class="text-sm">
                      <summary class="cursor-pointer text-amber-700 dark:text-amber-300 hover:text-amber-900 dark:hover:text-amber-100">查看原始代码</summary>
                      <pre class="mt-2 p-3 bg-amber-100 dark:bg-amber-900/30 rounded border text-amber-800 dark:text-amber-200 text-xs overflow-x-auto"><code>${mermaidCode}</code></pre>
                    </details>
                  </div>`;
                }
              }

              // 添加剩余内容
              processedContent += remainingContent;
            }
          }

          // 更新mermaid数据
          setMermaidData(newMermaidData);
        }

        // 渲染 markdown
        const html = md.render(processedContent);
        setRenderedContent(html);
      } catch (error) {
        console.error('Markdown rendering error:', error);
        setRenderedContent(`<p>渲染失败: ${error}</p>`);
      }
    };

    renderMarkdown();
  }, [content, skipMermaid, t]);

  // 计算适合容器的初始缩放比例
  const calculateFitScale = useCallback(() => {
    if (!containerRef.current || !contentRef.current) return 1;

    const container = containerRef.current;
    const content = contentRef.current;

    // 获取容器尺寸（减去一些边距）
    const containerWidth = container.clientWidth - 80; // 增加边距
    const containerHeight = container.clientHeight - 80;

    // 获取SVG的实际尺寸
    const svg = content.querySelector('svg');
    if (!svg) return 1;

    // 尝试从SVG的viewBox或width/height属性获取原始尺寸
    let svgWidth = svg.getBoundingClientRect().width;
    let svgHeight = svg.getBoundingClientRect().height;

    // 如果getBoundingClientRect返回0，尝试从属性获取
    if (svgWidth === 0 || svgHeight === 0) {
      const viewBox = svg.getAttribute('viewBox');
      if (viewBox) {
        const [, , width, height] = viewBox.split(' ').map(Number);
        svgWidth = width || svgWidth;
        svgHeight = height || svgHeight;
      } else {
        svgWidth = parseFloat(svg.getAttribute('width') || '800');
        svgHeight = parseFloat(svg.getAttribute('height') || '600');
      }
    }

    if (svgWidth === 0 || svgHeight === 0) return 1;

    // 计算适合的缩放比例
    const scaleX = containerWidth / svgWidth;
    const scaleY = containerHeight / svgHeight;

    // 取较小的比例，确保图表完全可见，但允许放大
    const fitScale = Math.min(scaleX, scaleY);

    // 设置最小缩放为0.5，最大为3
    const finalScale = Math.max(0.5, Math.min(3, fitScale));

    return finalScale;
  }, []);

  // 重置缩放和位置
  const resetTransform = useCallback(() => {
    if (fullscreenMermaid) {
      // 在全屏模式下，重置到适合的缩放比例
      const fitScale = calculateFitScale();
      setScale(fitScale > 0.1 ? fitScale : 1);
    } else {
      setScale(1);
    }
    setPosition({ x: 0, y: 0 });
  }, [fullscreenMermaid, calculateFitScale]);

  // 缩放处理
  const handleZoom = (delta: number) => {
    setScale(prev => {
      const newScale = Math.max(0.1, Math.min(5, prev + delta));
      return newScale;
    });
  };

  // 鼠标滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    handleZoom(delta);
  };

  // 鼠标拖拽开始
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // 左键
      setIsDragging(true);
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  };

  // 鼠标拖拽移动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  // 鼠标拖拽结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 触摸事件处理（移动端）
  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      // 单指拖拽
      const touch = e.touches[0];
      setIsDragging(true);
      setDragStart({ x: touch.clientX - position.x, y: touch.clientY - position.y });
    } else if (e.touches.length === 2) {
      // 双指缩放 - 记录初始距离
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      setDragStart({ x: distance, y: scale });
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault(); // 防止页面滚动

    if (e.touches.length === 1 && isDragging) {
      // 单指拖拽
      const touch = e.touches[0];
      setPosition({
        x: touch.clientX - dragStart.x,
        y: touch.clientY - dragStart.y
      });
    } else if (e.touches.length === 2) {
      // 双指缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      const scaleChange = distance / dragStart.x;
      const newScale = Math.max(0.1, Math.min(5, dragStart.y * scaleChange));
      setScale(newScale);
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // 处理mermaid点击事件
  const handleMermaidClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const mermaidContainer = target.closest('.mermaid-container');
    if (mermaidContainer) {
      const mermaidId = mermaidContainer.getAttribute('data-mermaid-id');
      if (mermaidId && mermaidData[mermaidId]) {
        setFullscreenMermaid(mermaidData[mermaidId]);
        // 先设置一个较大的初始缩放，然后再计算适合的缩放
        setScale(1.5);
        setPosition({ x: 0, y: 0 });
      }
    }
  };

  // 处理ESC键关闭全屏
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && fullscreenMermaid) {
        setFullscreenMermaid(null);
      }
    };

    if (fullscreenMermaid) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [fullscreenMermaid]);

  // 当全屏模态框关闭时重置状态
  useEffect(() => {
    if (!fullscreenMermaid) {
      resetTransform();
      setIsDragging(false);
    }
  }, [fullscreenMermaid, resetTransform]);

  // 当全屏模态框打开时，计算适合的初始缩放
  useEffect(() => {
    if (fullscreenMermaid && containerRef.current && contentRef.current) {
      // 多次尝试计算，确保SVG完全渲染
      const tryCalculateScale = (attempts = 0) => {
        const fitScale = calculateFitScale();

        // 如果计算出的缩放比例合理，或者已经尝试了足够多次，就应用它
        if (fitScale > 0.1 || attempts >= 5) {
          setScale(fitScale);
        } else if (attempts < 5) {
          // 如果还没有合理的缩放比例，再次尝试
          setTimeout(() => tryCalculateScale(attempts + 1), 100);
        }
      };

      // 延迟一点时间，确保DOM完全渲染
      setTimeout(() => tryCalculateScale(), 50);
    }
  }, [fullscreenMermaid, calculateFitScale]);

  return (
    <>
      <div
        className="prose prose-sm max-w-none dark:prose-invert markdown-content break-words overflow-wrap-anywhere
                   prose-headings:font-semibold prose-headings:text-foreground
                   prose-p:text-foreground/90 prose-p:leading-relaxed
                   prose-a:text-primary prose-a:no-underline hover:prose-a:underline
                   prose-strong:text-foreground prose-strong:font-semibold
                   prose-code:text-primary prose-code:bg-muted/50 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md prose-code:text-sm
                   prose-pre:bg-muted/30 prose-pre:border prose-pre:border-border/40 prose-pre:rounded-xl
                   prose-blockquote:border-l-primary prose-blockquote:bg-muted/20 prose-blockquote:rounded-r-lg
                   prose-ul:text-foreground/90 prose-ol:text-foreground/90
                   prose-li:text-foreground/90 prose-li:leading-relaxed
                   prose-table:border-border/40 prose-th:bg-muted/30 prose-td:border-border/30"
        onClick={handleMermaidClick}
        dangerouslySetInnerHTML={{ __html: renderedContent }}
      />

      {/* Mermaid全屏模态框 */}
      {fullscreenMermaid && (
        <div
          className={`fixed inset-0 z-50 bg-background/95 backdrop-blur-sm flex items-center justify-center ${
            isMobile ? 'p-0' : 'p-4'
          }`}
          onClick={() => setFullscreenMermaid(null)}
        >
          <div
            className={`bg-background shadow-2xl w-full h-full flex flex-col ${
              isMobile
                ? 'rounded-none'
                : 'rounded-2xl border border-border/30 max-w-7xl max-h-[95vh]'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 标题栏 */}
            <div className={`flex items-center justify-between border-b border-border/30 ${
              isMobile ? 'p-4' : 'p-6'
            }`}>
              <div className={isMobile ? 'flex-1 min-w-0' : ''}>
                <h3 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                  {t('mermaid.title')}
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {isMobile ? t('mermaid.instructions_mobile') : t('mermaid.instructions')}
                </p>
              </div>

              {/* 控制按钮组 */}
              <div className="flex items-center gap-2">
                <div className={`flex items-center gap-1 bg-muted/20 rounded-lg p-1 ${
                  isMobile ? 'scale-90' : ''
                }`}>
                  <button
                    onClick={() => handleZoom(0.2)}
                    className="p-2 rounded-md hover:bg-muted/40 transition-colors"
                    title={t('mermaid.zoom_in')}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleZoom(-0.2)}
                    className="p-2 rounded-md hover:bg-muted/40 transition-colors"
                    title={t('mermaid.zoom_out')}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </button>
                  <button
                    onClick={resetTransform}
                    className="p-2 rounded-md hover:bg-muted/40 transition-colors"
                    title={t('mermaid.reset_view')}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                  {!isMobile && (
                    <>
                      <div className="w-px h-6 bg-border/30 mx-1" />
                      <span className="text-xs text-muted-foreground px-2 min-w-[3rem] text-center">
                        {Math.round(scale * 100)}%
                      </span>
                    </>
                  )}
                </div>

                <button
                  onClick={() => setFullscreenMermaid(null)}
                  className="p-2 rounded-lg hover:bg-muted/20 transition-colors"
                  title={t('mermaid.close_fullscreen')}
                >
                  <X className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                </button>
              </div>
            </div>

            {/* 图表内容 */}
            <div
              ref={containerRef}
              className={`flex-1 overflow-hidden relative ${
                isMobile ? 'touch-none' : 'cursor-grab active:cursor-grabbing'
              }`}
              onWheel={handleWheel}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <div className="flex items-center justify-center w-full h-full">
                <div
                  ref={contentRef}
                  className="transition-transform duration-200 ease-out"
                  style={{
                    transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                    transformOrigin: 'center center'
                  }}
                  dangerouslySetInnerHTML={{ __html: fullscreenMermaid.svg }}
                />
              </div>

              {/* 移动端缩放指示器 */}
              {isMobile && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {Math.round(scale * 100)}%
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
