/**
 * Landing page configuration with dynamic URLs
 * This replaces hardcoded URLs in i18n JSON files
 */

import { serverUrls, getServerSocialLinks } from "./urls-server";

/**
 * Get footer configuration with dynamic URLs
 */
export const getFooterConfig = (locale: string = "en") => {
  const isZh = locale === "zh";
  
  return {
    name: "footer",
    brand: {
      title: "GTPlanner",
      description: isZh 
        ? "面向现代开发者的AI驱动规划工具。通过智能项目规划和文档生成增强你的vibe coding工作流程。"
        : "AI-powered planning tool for modern developers. Design systems, generate docs, and enhance your development workflow.",
      logo: {
        src: "/logo.png",
        alt: "GTPlanner"
      },
      url: isZh ? "/zh" : "/"
    },
    copyright: isZh 
      ? "© 2025 • GTPlanner 版权所有。"
      : "© 2025 • GTPlanner All rights reserved.",
    nav: {
      items: [
        {
          title: isZh ? "产品" : "Product",
          children: [
            {
              title: isZh ? "功能特点" : "Features",
              url: "/#feature",
              target: "_self"
            },
            {
              title: isZh ? "演示" : "Demo",
              url: "/chat",
              target: "_self"
            },
            {
              title: isZh ? "使用场景" : "Use Cases",
              url: "/#usage",
              target: "_self"
            }
          ]
        },
        {
          title: isZh ? "资源" : "Resources",
          children: [
            {
              title: isZh ? "文档" : "Documentation",
              url: "/#faq",
              target: "_self"
            },
            {
              title: "GitHub",
              url: serverUrls.social.github,
              target: "_blank"
            }
          ]
        }
      ]
    },
    social: {
      items: [
        {
          title: "Github",
          icon: "RiGithubFill",
          url: serverUrls.social.github,
          target: "_blank"
        },
        {
          title: "Email",
          icon: "RiMailLine",
          url: serverUrls.contact.supportEmailUrl,
          target: "_self"
        }
      ]
    },
    agreement: {
      items: [
        {
          title: isZh ? "隐私政策" : "Privacy Policy",
          url: "/privacy-policy"
        },
        {
          title: isZh ? "服务条款" : "Terms of Service",
          url: "/terms-of-service"
        }
      ]
    }
  };
};

/**
 * Get navigation configuration with dynamic URLs
 */
export const getNavConfig = (locale: string = "en") => {
  const isZh = locale === "zh";
  
  return {
    items: [
      {
        title: isZh ? "功能特点" : "Features",
        url: "/#feature",
        icon: "HiOutlineSparkles"
      },
      {
        title: isZh ? "演示" : "Demo",
        url: "/chat",
        icon: "RiRobot2Line"
      },
      {
        title: isZh ? "使用场景" : "Use Cases",
        url: "/#usage",
        icon: "RiCodeLine"
      }
    ]
  };
};

/**
 * Get header buttons configuration
 */
export const getHeaderButtonsConfig = (locale: string = "en") => {
  const isZh = locale === "zh";
  
  return [
    {
      title: isZh ? "试用演示" : "Try Demo",
      url: "/chat",
      target: "_self",
      variant: "link",
      icon: "RiArrowRightLine"
    }
  ];
};

/**
 * Get hero buttons configuration
 */
export const getHeroButtonsConfig = (locale: string = "en") => {
  const isZh = locale === "zh";
  
  return [
    {
      title: isZh ? "试用演示" : "Try Demo",
      icon: "RiRobot2Line",
      url: "/chat",
      target: "_self",
      variant: "default"
    },
    {
      title: isZh ? "查看功能" : "View Features",
      icon: "RiSparklingFill",
      url: "/#feature",
      target: "_self",
      variant: "outline"
    }
  ];
};
