import { ChatState } from '@/types/conversation';

// 定义Message类型，避免循环依赖
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  type?: 'plan' | 'message' | 'document' | 'analysis';
}

// 示例数据 - 将在组件内部使用国际化
export const EXAMPLE_IDS = ["youtube", "document", "report", "research", "content", "strategy"];

// 国际化错误处理
export const getLocalizedError = (error: string, t: (key: string) => string) => {
  switch (error) {
    case "Plan generation failed, please try again.":
      return t('errors.plan_failed');
    case "Document generation failed, please try again.":
      return t('errors.document_failed');
    case "Network error, please check your connection and try again.":
      return t('errors.network_error');
    default:
      return error;
  }
};

// 错误恢复处理 - 清理所有流式状态并恢复到对话状态
export const handleErrorRecovery = (
  setState: (state: ChatState) => void,
  setStreamingMessage: (message: string) => void,
  setStreamingPlan: (plan: string) => void,
  setStreamingDoc: (doc: string) => void,
  setStreamingAnalysis: (analysis: string) => void,
  setLoadingStartTime: (time: number) => void
) => {
  // 清理所有流式状态
  setStreamingMessage("");
  setStreamingPlan("");
  setStreamingDoc("");
  setStreamingAnalysis("");

  // 重置加载时间
  setLoadingStartTime(0);

  // 恢复到对话状态，确保用户可以继续输入
  setState(ChatState.CONVERSATION);
};

// 根据消息恢复会话状态
export const restoreSessionState = (
  messages: Message[],
  setState: (state: ChatState) => void,
  setShowDocPanel: (show: boolean) => void,
  setIsEditingPlan: (editing: boolean) => void,
  setEditablePlan: (plan: string) => void,
  setEditableDoc: (doc: string) => void,
  setIsViewingHistoryDoc: (viewing: boolean) => void,
  setCurrentRequirement: (requirement: string) => void,
  setShortPlanResult: (result: string) => void,
  setLongDocResult: (result: string) => void
) => {
  // 首先重置所有相关状态
  setShowDocPanel(false);
  setIsEditingPlan(false);
  setEditablePlan("");
  setEditableDoc("");
  setIsViewingHistoryDoc(false);
  setCurrentRequirement("");
  setShortPlanResult("");
  setLongDocResult("");

  if (messages.length === 0) {
    setState(ChatState.WELCOME);
    return;
  }

  // 获取最后一条消息来确定当前状态
  const lastMessage = messages[messages.length - 1];
  const userMessages = messages.filter(msg => msg.role === 'user');
  const assistantMessages = messages.filter(msg => msg.role === 'assistant');

  // 查找所有类型的消息
  const planMessages = messages.filter(msg => msg.type === 'plan');
  const docMessages = messages.filter(msg => msg.type === 'document');

  // 获取最新的规划和文档消息
  const latestPlanMessage = planMessages[planMessages.length - 1];
  const latestDocMessage = docMessages[docMessages.length - 1];

  // 根据最后一条消息的类型来确定状态
  if (lastMessage?.type === 'document') {
    // 最后一条是文档消息，设置为文档结果状态
    setState(ChatState.LONG_RESULT);
    setLongDocResult(lastMessage.content);
    setEditableDoc(lastMessage.content);
    setShowDocPanel(true);

    // 如果有规划消息，也要恢复规划状态
    if (latestPlanMessage) {
      setShortPlanResult(latestPlanMessage.content);
      setEditablePlan(latestPlanMessage.content);
    }
  } else if (lastMessage?.type === 'plan') {
    // 最后一条是规划消息，设置为规划确认状态
    setState(ChatState.SHORT_CONFIRM);
    setShortPlanResult(lastMessage.content);
    setEditablePlan(lastMessage.content);
  } else if (lastMessage?.type === 'message' || lastMessage?.role === 'user') {
    // 最后一条是普通消息或用户消息，设置为对话状态
    setState(ChatState.CONVERSATION);

    // 恢复之前的规划和文档状态（如果有的话）
    if (latestDocMessage) {
      setLongDocResult(latestDocMessage.content);
      setEditableDoc(latestDocMessage.content);
      // 如果有文档内容，自动打开侧边栏
      setShowDocPanel(true);
    }
    if (latestPlanMessage) {
      setShortPlanResult(latestPlanMessage.content);
      setEditablePlan(latestPlanMessage.content);
    }
  } else if (userMessages.length === 1 && assistantMessages.length === 0) {
    // 只有一条用户消息且没有助手回复时，可能是需求处理中断
    setState(ChatState.CONVERSATION);
    setCurrentRequirement(userMessages[0].content);
  } else {
    setState(ChatState.WELCOME);
  }

  // 恢复当前需求（仅在有规划相关消息时）
  if (latestPlanMessage || latestDocMessage) {
    if (userMessages.length > 0) {
      setCurrentRequirement(userMessages[0].content);
    }
  }
};

// 自动滚动到底部
export const scrollToBottom = () => {
  const scrollArea = document.querySelector('.chat-scroll-area');
  if (scrollArea) {
    // 使用 setTimeout 确保 DOM 更新完成后再滚动
    setTimeout(() => {
      // 滚动到底部，确保有足够的空间
      scrollArea.scrollTop = scrollArea.scrollHeight + 100;
    }, 100);
  }
};

// 检测移动端
export const checkMobile = () => {
  return window.innerWidth < 1024;
};

export const chatHelpers = {
  EXAMPLE_IDS,
  getLocalizedError,
  handleErrorRecovery,
  restoreSessionState,
  scrollToBottom,
  checkMobile,
};
