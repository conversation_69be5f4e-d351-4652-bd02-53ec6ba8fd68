"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Share2, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface ShareDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sessionId: string;
  sessionTitle: string;
  isPublic: boolean;
  onToggleShare: (sessionId: string, currentState: boolean) => Promise<void>;
}

export function ShareDialog({
  open,
  onOpenChange,
  sessionId,
  sessionTitle,
  isPublic,
  onToggleShare,
}: ShareDialogProps) {
  const t = useTranslations('chat');
  const [isSharing, setIsSharing] = useState(isPublic);
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // 同步外部传入的 isPublic 状态
  useEffect(() => {
    setIsSharing(isPublic);
  }, [isPublic]);

  // 生成分享链接 NEXT_PUBLIC_WEB_URL
  const shareUrl = typeof window !== 'undefined' 
    ? `${window.location.origin}/share/${sessionId}` 
    : '';

  // 处理分享状态切换
  const handleToggleShare = async () => {
    setIsLoading(true);
    try {
      await onToggleShare(sessionId, isSharing);
      setIsSharing(!isSharing);
    } catch (error) {
      console.error('Toggle share failed:', error);
      toast.error('分享设置失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 复制分享链接
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast.success(t('session_list.copy_success'));
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
      toast.error('复制失败，请重试');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            分享对话
          </DialogTitle>
          <DialogDescription>
            分享 "{sessionTitle}" 对话的公共链接
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 分享开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="share-toggle" className="text-sm font-medium">
                公开分享
              </Label>
              <p className="text-xs text-muted-foreground">
                任何拥有链接的人都可以查看这个对话
              </p>
            </div>
            <Switch
              id="share-toggle"
              checked={isSharing}
              onCheckedChange={handleToggleShare}
              disabled={isLoading}
            />
          </div>

          {/* 分享链接 */}
          {isSharing && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">分享链接</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={shareUrl}
                  readOnly
                  className="flex-1 bg-muted"
                  onClick={(e) => e.currentTarget.select()}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCopyLink}
                  className="shrink-0"
                >
                  {copied ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                  {copied ? "已复制" : "复制"}
                </Button>
              </div>
              
              {/* 提示信息 */}
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 shrink-0" />
                  <div className="text-xs text-blue-700 dark:text-blue-300">
                    <p className="font-medium mb-1">分享提示</p>
                    <ul className="space-y-1 text-blue-600 dark:text-blue-400">
                      <li>• 拥有链接的任何人都可以查看此对话</li>
                      <li>• 观看者无法继续对话或编辑内容</li>
                      <li>• 您可以随时取消分享</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            关闭
          </Button>
          {isSharing && (
            <Button onClick={handleCopyLink}>
              <Copy className="w-4 h-4 mr-2" />
              复制链接
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 