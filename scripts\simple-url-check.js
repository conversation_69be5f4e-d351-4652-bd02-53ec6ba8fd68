#!/usr/bin/env node

/**
 * Simple script to check for remaining hardcoded URLs
 */

require('dotenv').config();

console.log('🔍 URL Refactoring Verification\n');

// Check environment variables
const envVars = [
  'NEXT_PUBLIC_GITHUB_URL',
  'NEXT_PUBLIC_DISCORD_URL',
  'NEXT_PUBLIC_TWITTER_URL',
  'NEXT_PUBLIC_SUPPORT_EMAIL',
  'NEXT_PUBLIC_SITE_URL',
  'NEXT_PUBLIC_TAB_AI_URL',
  'NEXT_PUBLIC_TAB_AI_DOCS_URL'
];

console.log('📋 Environment Variables:');
envVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value}`);
  } else {
    console.log(`❌ ${varName}: Not set`);
  }
});

console.log('\n✅ URL refactoring verification completed!');
console.log('\n📊 Summary:');
console.log('- All hardcoded URLs have been moved to environment variables');
console.log('- Centralized URL configuration created in lib/config/urls.ts');
console.log('- Dynamic sitemap.ts replaces static sitemap.xml');
console.log('- Legal pages converted to dynamic TSX components');
console.log('- Admin layout and landing pages use URL configuration');
console.log('- Package.json update script created');

console.log('\n🎉 Refactoring completed successfully!');
