# Next.js 14 Compatibility Guide

## Overview
This document outlines the Next.js 14 specific implementations and compatibility considerations for the URL refactoring in GTPlanner.

## ✅ Next.js 14 App Router Compatibility

### Environment Variables
- **All URLs use `NEXT_PUBLIC_` prefixed environment variables** for client-side access
- **Server components** can access all environment variables
- **Client components** can only access `NEXT_PUBLIC_` prefixed variables

### File Structure
```
lib/config/
├── urls.ts          # Universal URL config (client + server compatible)
├── urls-server.ts   # Server-only URL config (for server components)
└── landing.ts       # Landing page configuration (server-side)
```

## 🔧 Implementation Details

### 1. Universal URL Configuration (`lib/config/urls.ts`)
- Uses `getEnvVar()` helper function for client/server compatibility
- Handles both server-side and client-side environment variable access
- Safe for use in both server and client components

### 2. Server-Only Configuration (`lib/config/urls-server.ts`)
- Optimized for server components and API routes
- Direct `process.env` access (all variables available)
- Used in:
  - `app/sitemap.ts`
  - `app/[locale]/(admin)/layout.tsx`
  - `lib/config/landing.ts`

### 3. Client Components
- Use the universal `urls` from `lib/config/urls.ts`
- Automatically handles client-side environment variable access
- Examples:
  - `app/[locale]/(default)/(console)/my-invites/page.tsx`
  - `app/[locale]/(default)/(console)/my-orders/page.tsx`

## 🚀 App Router Features Used

### 1. Dynamic Sitemap (`app/sitemap.ts`)
```typescript
import { MetadataRoute } from 'next'
import { serverUrls } from '@/lib/config/urls-server'

export default function sitemap(): MetadataRoute.Sitemap {
  // Uses environment variables for dynamic URL generation
}
```

### 2. Metadata Generation
```typescript
export const metadata: Metadata = {
  title: 'Privacy Policy | GTPlanner',
  description: 'Privacy policy for GTPlanner...',
};
```

### 3. Server Components
- All layout components are server components
- Direct environment variable access in server context
- Optimized for performance with server-side URL resolution

## 🔍 Environment Variable Access Patterns

### Server Components (Recommended)
```typescript
import { serverUrls } from '@/lib/config/urls-server'

// Direct access to all environment variables
const githubUrl = serverUrls.social.github
```

### Client Components
```typescript
import { urls } from '@/lib/config/urls'

// Safe client-side access with fallbacks
const githubUrl = urls.social.github
```

### Universal Components
```typescript
import { urls } from '@/lib/config/urls'

// Works in both server and client contexts
const githubUrl = urls.social.github
```

## 📋 Migration Checklist

- [x] All environment variables use `NEXT_PUBLIC_` prefix
- [x] Server components use `urls-server.ts`
- [x] Client components use `urls.ts`
- [x] Sitemap uses App Router `MetadataRoute.Sitemap`
- [x] Legal pages have proper metadata exports
- [x] Landing page configuration is server-side optimized
- [x] Admin layout uses server-side URL configuration

## 🛠️ Development Guidelines

### When to Use Each Configuration

1. **Use `lib/config/urls-server.ts`** for:
   - Server components
   - API routes
   - Sitemap generation
   - Metadata generation

2. **Use `lib/config/urls.ts`** for:
   - Client components
   - Universal components
   - Components that might be used in both contexts

### Environment Variable Naming
- All public URLs must use `NEXT_PUBLIC_` prefix
- Server-only variables can omit the prefix
- Fallback values are provided for all URLs

## 🔧 Build and Runtime Considerations

### Build Time
- Environment variables are embedded at build time for client-side access
- Server-side variables are resolved at runtime

### Runtime
- Client components receive pre-built environment variables
- Server components have access to all runtime environment variables

## 🚨 Common Pitfalls to Avoid

1. **Don't use `process.env` directly in client components**
   ```typescript
   // ❌ Wrong - won't work in client components
   const url = process.env.NEXT_PUBLIC_GITHUB_URL
   
   // ✅ Correct - use the configuration
   import { urls } from '@/lib/config/urls'
   const url = urls.social.github
   ```

2. **Don't mix server and client configurations**
   ```typescript
   // ❌ Wrong - server config in client component
   import { serverUrls } from '@/lib/config/urls-server'
   
   // ✅ Correct - universal config
   import { urls } from '@/lib/config/urls'
   ```

3. **Don't forget `NEXT_PUBLIC_` prefix for client-accessible variables**
   ```env
   # ❌ Wrong - not accessible in client
   GITHUB_URL=https://github.com/gtplanner
   
   # ✅ Correct - accessible everywhere
   NEXT_PUBLIC_GITHUB_URL=https://github.com/gtplanner
   ```

## 📚 Additional Resources

- [Next.js 14 Environment Variables](https://nextjs.org/docs/app/building-your-application/configuring/environment-variables)
- [Next.js 14 App Router](https://nextjs.org/docs/app)
- [Next.js 14 Metadata API](https://nextjs.org/docs/app/building-your-application/optimizing/metadata)
