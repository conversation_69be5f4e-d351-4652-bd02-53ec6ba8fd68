// 统一对话接口的类型定义

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  message_type: 'message' | 'plan' | 'document' | 'analysis';
  timestamp: number;
  metadata?: any;
}

export interface ConversationRequest {
  message: string;
  conversation_history: ChatMessage[];
  session_id?: string;
  language?: string;
  action?: 'generate_document'; // 明确的操作类型
  context?: {
    current_plan?: string;
    current_document?: string;
    user_intent_hint?: string;
  };
}

export interface ConversationAction {
  type: 'plan' | 'document' | 'suggestion';
  content: string;
  title?: string;
  metadata?: {
    language?: string;
    version?: number;
    based_on?: string;
    confidence?: number;
  };
}

export interface ConversationResponse {
  success: boolean;
  data: {
    response: string;
    intent: 'conversation' | 'requirement' | 'optimization' | 'document_generation';
    confidence: number;
    actions: ConversationAction[];
    metadata: {
      language: string;
      session_id?: string;
      processing_time?: number;
    };
  };
  error?: string;
}

// 简化的聊天状态
export enum ChatState {
  WELCOME = "welcome",
  CONVERSATION = "conversation",
  LOADING = "loading",
  SHORT_CONFIRM = "short_confirm",
  LONG_RESULT = "long_result",
  SHORT_LOADING = "short_loading",
  LONG_LOADING = "long_loading",
  SHORT_CHAT_LOADING = "short_chat_loading"
}

// 消息显示类型
export interface MessageDisplay {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  type: 'message' | 'plan' | 'document';
  actions?: ConversationAction[];
}
