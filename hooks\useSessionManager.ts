import { useState } from 'react';
import { Message } from './useChatState';

export interface SessionManagerHook {
  // 会话相关状态
  currentSessionId: string | null;
  setCurrentSessionId: (id: string | null) => void;
  showSessionList: boolean;
  setShowSessionList: (show: boolean) => void;
  sessionListCollapsed: boolean;
  setSessionListCollapsed: (collapsed: boolean) => void;
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  isNewChatPreparing: boolean;
  setIsNewChatPreparing: (preparing: boolean) => void;
  sessionListRefreshTrigger: number;
  setSessionListRefreshTrigger: (trigger: number | ((prev: number) => number)) => void;
  
  // 会话操作方法
  toggleSessionList: (isMobile: boolean) => void;
  isInNewChatState: () => boolean;
  
  // API方法
  saveMessageToSession: (sessionId: string, role: 'user' | 'assistant', content: string, type?: 'plan' | 'message' | 'document' | 'analysis') => Promise<void>;
  updateMessageInSession: (sessionId: string, messageId: string, content: string, type?: 'plan' | 'message' | 'document' | 'analysis') => Promise<void>;
  loadSessionMessages: (sessionId: string) => Promise<Message[]>;
  createNewSessionIfNeeded: (firstMessage?: string, customTitle?: string) => Promise<string | null>;
}

export function useSessionManager(messages: Message[]): SessionManagerHook {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [showSessionList, setShowSessionList] = useState(true);
  const [sessionListCollapsed, setSessionListCollapsed] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isNewChatPreparing, setIsNewChatPreparing] = useState(true);
  const [sessionListRefreshTrigger, setSessionListRefreshTrigger] = useState(0);

  // 切换会话列表显示/隐藏
  const toggleSessionList = (isMobile: boolean) => {
    if (isMobile) {
      setShowSessionList(!showSessionList);
    } else {
      // 桌面端：收起 -> 展开 -> 隐藏 -> 展开
      if (!showSessionList) {
        setShowSessionList(true);
        setSidebarCollapsed(false);
      } else if (!sidebarCollapsed) {
        setSidebarCollapsed(true);
      } else {
        setShowSessionList(false);
        setSidebarCollapsed(false);
      }
    }
  };

  // 判断是否在新对话准备状态
  const isInNewChatState = () => {
    return currentSessionId === null && messages.length === 0;
  };

  // 保存消息到会话
  const saveMessageToSession = async (sessionId: string, role: 'user' | 'assistant', content: string, type?: 'plan' | 'message' | 'document' | 'analysis') => {
    try {
      await fetch(`/api/chat/sessions/${sessionId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role,
          content,
          message_type: type || 'message',
        }),
      });
    } catch (error) {
      console.error('Failed to save message:', error);
    }
  };

  // 更新会话中的消息
  const updateMessageInSession = async (sessionId: string, messageId: string, content: string, type?: 'plan' | 'message' | 'document' | 'analysis') => {
    try {
      await fetch(`/api/chat/sessions/${sessionId}/messages/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          message_type: type || 'message',
        }),
      });
    } catch (error) {
      console.error('Failed to update message:', error);
    }
  };

  // 加载会话消息
  const loadSessionMessages = async (sessionId: string): Promise<Message[]> => {
    try {
      const response = await fetch(`/api/chat/sessions/${sessionId}/messages`);
      if (response.ok) {
        const result = await response.json();
        const dbMessages = result.data || [];

        // 转换数据库消息格式为组件状态格式
        const formattedMessages = dbMessages.map((msg: any) => ({
          id: msg.uuid,
          content: msg.content,
          role: msg.role,
          timestamp: new Date(msg.created_at).getTime(),
          type: msg.message_type,
        }));

        return formattedMessages;
      }
      return [];
    } catch (error) {
      console.error('Failed to load session messages:', error);
      return [];
    }
  };

  // 创建新会话（当用户开始对话时）
  const createNewSessionIfNeeded = async (firstMessage?: string, customTitle?: string): Promise<string | null> => {
    if (!currentSessionId) {
      try {
        // 生成会话标题：优先使用自定义标题，其次使用消息前10字符，最后使用默认标题
        let title = customTitle;
        if (!title && firstMessage) {
          title = firstMessage.length > 10 ? firstMessage.substring(0, 10) + '...' : firstMessage;
        }
        if (!title) {
          title = `新会话 ${new Date().toLocaleString('zh-CN')}`;
        }

        const response = await fetch('/api/chat/sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ title }),
        });

        if (response.ok) {
          const result = await response.json();
          setCurrentSessionId(result.data.uuid);
          setIsNewChatPreparing(false);
          // 触发会话列表刷新
          setSessionListRefreshTrigger(prev => prev + 1);
          return result.data.uuid;
        }
      } catch (error) {
        console.error('Failed to create new session:', error);
      }
    }
    return currentSessionId;
  };

  return {
    currentSessionId,
    setCurrentSessionId,
    showSessionList,
    setShowSessionList,
    sessionListCollapsed,
    setSessionListCollapsed,
    sidebarCollapsed,
    setSidebarCollapsed,
    isNewChatPreparing,
    setIsNewChatPreparing,
    sessionListRefreshTrigger,
    setSessionListRefreshTrigger,
    toggleSessionList,
    isInNewChatState,
    saveMessageToSession,
    updateMessageInSession,
    loadSessionMessages,
    createNewSessionIfNeeded,
  };
}
