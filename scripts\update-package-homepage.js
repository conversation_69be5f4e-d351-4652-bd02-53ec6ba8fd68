#!/usr/bin/env node

/**
 * Script to update package.json homepage URL from environment variable
 * Usage: node scripts/update-package-homepage.js
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

const packageJsonPath = path.join(__dirname, '..', 'package.json');

try {
  // Read current package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Update homepage from environment variable
  const homepageUrl = process.env.NEXT_PUBLIC_HOMEPAGE_URL || process.env.NEXT_PUBLIC_SITE_URL;
  
  if (homepageUrl) {
    packageJson.homepage = homepageUrl;
    
    // Write updated package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
    console.log(`✅ Updated package.json homepage to: ${homepageUrl}`);
  } else {
    console.log('⚠️  No NEXT_PUBLIC_HOMEPAGE_URL or NEXT_PUBLIC_SITE_URL found in environment variables');
  }
} catch (error) {
  console.error('❌ Error updating package.json:', error.message);
  process.exit(1);
}
