{"template": "gtplanner-template", "theme": "light", "header": {"brand": {"title": "GTPlanner", "logo": {"src": "/logo.svg", "alt": "GTPlanner"}, "url": "/zh"}, "nav": {"items": [{"title": "功能特点", "url": "/zh/#feature", "icon": "HiOutlineSparkles"}, {"title": "演示", "url": "/zh/chat", "icon": "RiRobot2Line"}, {"title": "使用场景", "url": "/zh/#usage", "icon": "RiCodeLine"}]}, "buttons": [{"title": "试用演示", "url": "/zh/chat", "target": "_self", "variant": "link", "icon": "RiArrowRightLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Vibe Coding 专用规划工具", "highlight_text": "Vibe Coding", "description": "面向现代开发者的AI驱动规划工具。<br/>设计系统、生成文档，提升你的开发工作流程。", "announcement": {"label": "新功能", "title": "🚀 AI驱动的智能规划", "url": "/zh/chat"}, "tip": "✨ 革新你的开发工作流程", "buttons": [{"title": "试用演示", "icon": "RiRobot2Line", "url": "/zh/chat", "target": "_self", "variant": "default"}, {"title": "查看功能", "icon": "RiSparklingFill", "url": "/zh/#feature", "target": "_self", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"disabled": true, "title": "基于现代开发工具构建", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}]}, "introduce": {"name": "introduce", "title": "什么是 GTPlanner", "label": "介绍", "description": "GTPlanner 是一个面向现代开发者的AI驱动规划工具。它帮助你设计系统、生成文档，并提升你的vibe coding工作流程。", "items": [{"title": "AI智能规划", "description": "通过先进的AI辅助生成智能的项目规划和系统架构。", "icon": "RiRobot2Line"}, {"title": "智能文档生成", "description": "从你的规划会话中自动创建全面的技术文档。", "icon": "RiFileTextLine"}, {"title": "开发者优化", "description": "专为现代开发工作流程和vibe coding实践而构建。", "icon": "RiCodeLine"}]}, "benefit": {"name": "benefit", "title": "为什么选择 GTPlanner", "label": "优势", "description": "通过AI驱动的规划和文档生成来提升你的开发工作流程。", "items": [{"title": "智能规划", "description": "AI分析你的需求并生成结构化的项目规划，包含清晰的实施步骤。", "icon": "RiBrainLine"}, {"title": "Vibe Coding 就绪", "description": "为Cursor、Windsurf和GitHub Copilot等现代AI开发工具优化，实现无缝编码流程。", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "文档自动化", "description": "自动生成全面的技术文档、API规范和架构图。", "icon": "RiFileTextLine"}]}, "usage": {"name": "usage", "title": "开发者使用场景", "description": "了解GTPlanner如何增强不同的开发场景：", "text_align": "center", "items": [{"title": "系统架构设计", "description": "在AI辅助下规划复杂的系统架构。生成组件图和数据流图。"}, {"title": "API开发规划", "description": "设计RESTful API和GraphQL模式。生成端点文档和集成指南。"}, {"title": "功能实现", "description": "将复杂功能分解为可管理的任务。创建用户故事和验收标准。"}, {"title": "技术文档", "description": "为你的项目生成全面的文档、README文件和部署指南。"}]}, "feature": {"name": "feature", "title": "核心功能", "description": "智能开发规划和文档生成所需的一切。", "items": [{"title": "AI规划助手", "description": "先进的AI分析需求并生成结构化的项目规划和实施步骤。", "icon": "RiRobot2Line"}, {"title": "交互式聊天界面", "description": "自然的对话流程，用于迭代地完善规划和生成文档。", "icon": "RiChatSmile3Line"}, {"title": "文档生成", "description": "自动创建技术文档、API规范和架构图。", "icon": "RiFileTextLine"}, {"title": "Vibe Coding集成", "description": "为Cursor和GitHub Copilot等现代AI开发工具优化输出。", "icon": "RiCodeLine"}, {"title": "多语言支持", "description": "完整的国际化支持，无缝语言切换。", "icon": "RiTranslate2"}, {"title": "导出和分享", "description": "以多种格式导出规划和文档，便于团队协作。", "icon": "RiShareLine"}]}, "faq": {"name": "faq", "label": "常见问题", "title": "常见问题", "description": "了解更多关于GTPlanner以及它如何增强你的开发工作流程。", "items": [{"title": "什么是GTPlanner，它是如何工作的？", "description": "GTPlanner是一个面向现代开发者的AI驱动规划工具。它分析你的需求并生成结构化的项目规划、系统架构和全面的文档，以增强你的vibe coding工作流程。"}, {"title": "GTPlanner如何支持vibe coding？", "description": "GTPlanner生成针对Cursor、Windsurf和GitHub Copilot等现代AI开发工具优化的输出。结构化的规划和文档帮助你保持编码流程和生产力。"}, {"title": "我可以用GTPlanner规划什么类型的项目？", "description": "GTPlanner支持各种开发项目，包括Web应用程序、API、系统架构、微服务等。它特别适用于规划复杂的软件系统和技术实现。"}, {"title": "我可以导出和分享生成的规划吗？", "description": "是的！GTPlanner允许你以多种格式导出规划和文档，包括Markdown，可以轻松与团队分享或集成到项目仓库中。"}, {"title": "GTPlanner适合团队协作吗？", "description": "绝对适合！生成的文档和规划设计得清晰全面，非常适合团队协作和开发团队间的知识分享。"}, {"title": "GTPlanner支持多种编程语言吗？", "description": "是的，GTPlanner可以为使用各种编程语言和框架的项目生成规划和文档。AI理解不同的技术栈并提供相关建议。"}]}, "cta": {"name": "cta", "title": "开始AI规划", "description": "用智能规划革新你的开发工作流程。", "buttons": [{"title": "试用演示", "url": "/zh/chat", "target": "_self", "icon": "RiRobot2Line"}, {"title": "查看功能", "url": "/zh/#feature", "target": "_self", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "GTPlanner", "description": "面向现代开发者的AI驱动规划工具。通过智能项目规划和文档生成增强你的vibe coding工作流程。", "logo": {"src": "/logo.svg", "alt": "GTPlanner"}, "url": "/zh"}, "copyright": "© 2025 • GTPlanner 版权所有。", "nav": {"items": [{"title": "产品", "children": [{"title": "功能特点", "url": "/zh/#feature", "target": "_self"}, {"title": "演示", "url": "/zh/chat", "target": "_self"}, {"title": "使用场景", "url": "/zh/#usage", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "/zh/#faq", "target": "_self"}, {"title": "GitHub", "url": "#", "target": "_blank"}]}]}, "social": {"items": [{"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "#", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "#", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/zh/privacy-policy"}, {"title": "服务条款", "url": "/zh/terms-of-service"}]}}}