"use client";
import { MessageItem } from "./message-item";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  type?: 'plan' | 'message' | 'document' | 'analysis';
}

interface MessageListProps {
  messages: Message[];
  showPlans?: boolean;
  locale: string;
  t: any;
  onViewDocument?: (content: string) => void;
  isSharedView?: boolean;
}

export function MessageList({
  messages,
  showPlans = true,
  locale,
  t,
  onViewDocument,
  isSharedView = false
}: MessageListProps) {
  const filteredMessages = messages.filter(message =>
    message.role === 'user' ||
    (message.type === 'plan' && showPlans) ||
    // 显示document类型的消息，让用户可以查看历史文档版本
    (message.type === 'document' && showPlans) ||
    // analysis类型的消息不在对话记录中显示，只在文档面板中显示
    // (message.type === 'analysis' && showPlans) ||
    (message.type === 'message' && message.role === 'assistant')
  );

  return (
    <div className="space-y-6 mb-8 px-1">
      {/* 显示过滤后的消息 */}
      {filteredMessages.map((message, index) => (
        <div
          key={message.id}
          className="message-enter"
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          <MessageItem
            message={message}
            locale={locale}
            t={t}
            onViewDocument={onViewDocument}
            isSharedView={isSharedView}
          />
        </div>
      ))}
    </div>
  );
}
