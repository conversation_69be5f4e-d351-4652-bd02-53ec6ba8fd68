"use client";
import { MessageCircle } from "lucide-react";
import { useTranslations } from "next-intl";

interface StreamingMessageProps {
  content: string;
}

export function StreamingMessage({ content }: StreamingMessageProps) {
  const t = useTranslations('chat');
  if (!content) return null;

  return (
    <div className="flex justify-start">
      <div className="max-w-[85%] md:max-w-[80%] rounded-2xl px-4 py-3 bg-gradient-to-br from-muted/60 to-muted/40 shadow-sm border border-border/30">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-1.5 rounded-lg bg-primary/10">
            <MessageCircle className="w-4 h-4 text-primary" />
          </div>
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-xs font-medium text-primary">{t('loading.replying')}</span>
          </div>
        </div>
        <div className="text-sm whitespace-pre-wrap leading-relaxed text-foreground/90 bg-background/30 rounded-xl p-3 border border-border/20">
          {content}
          <span className="inline-block w-2 h-4 bg-primary/60 animate-pulse ml-1 rounded-sm"></span>
        </div>
      </div>
    </div>
  );
}
